# 尺寸规格价格并排布局说明

## 优化目标

将尺寸选择卡片中的规格名称和价格改为并排显示，提高空间利用率和视觉效果。

## 布局变化

### 修改前（垂直布局）
```
┌─────────────┐  ┌─────────────┐
│ [尺寸图片]  │  │ [尺寸图片]  │
│   9英寸     │  │   12英寸    │
│  (价格)     │  │  (价格)     │
└─────────────┘  └─────────────┘
```

### 修改后（并排布局）
```
┌─────────────┐  ┌─────────────┐
│ [尺寸图片]  │  │ [尺寸图片]  │
│ 9英寸 (价格)│  │12英寸 (价格)│
└─────────────┘  └─────────────┘
```

## 技术实现

### 1. HTML 结构调整
```vue
<!-- 修改前：垂直布局 -->
<div class="size-info">
  <div class="size-name">{{ priceItem.exceptionKey }}</div>
  <div class="size-price">{{ priceItem.exceptionText }}</div>
</div>

<!-- 修改后：并排布局 -->
<div class="size-info">
  <div class="size-details">
    <span class="size-name">{{ priceItem.exceptionKey }}</span>
    <span class="size-price">{{ priceItem.exceptionText }}</span>
  </div>
</div>
```

### 2. CSS 样式实现
```scss
.size-info {
  .size-details {
    display: flex;                    // 弹性布局
    justify-content: space-between;   // 两端对齐
    align-items: center;              // 垂直居中
    gap: 8px;                         // 间距

    .size-name {
      font-size: 12px;
      font-weight: 600;
      color: #333;
      flex: 1;                        // 占据剩余空间
    }

    .size-price {
      font-size: 11px;
      color: #e60012;
      font-weight: 500;
      white-space: nowrap;            // 防止换行
    }
  }
}
```

## 设计特点

### 🎯 **空间利用**
- **水平布局**：充分利用卡片宽度
- **紧凑设计**：减少垂直空间占用
- **信息密度**：在有限空间内展示更多信息

### 🎨 **视觉效果**
- **两端对齐**：规格名称左对齐，价格右对齐
- **视觉平衡**：左右信息形成平衡
- **层次清晰**：图片 → 文字信息的清晰层次

### 📱 **响应式设计**
- **弹性布局**：flex 布局自适应宽度
- **防止换行**：white-space: nowrap 保证价格不换行
- **间距控制**：8px gap 保证适当间距

## 布局细节

### Flexbox 属性说明

#### justify-content: space-between
- **作用**：两端对齐，中间自动分配空间
- **效果**：规格名称靠左，价格靠右
- **优势**：自动适应不同宽度

#### align-items: center
- **作用**：垂直居中对齐
- **效果**：规格和价格在同一水平线
- **优势**：视觉协调统一

#### flex: 1
- **作用**：规格名称占据剩余空间
- **效果**：价格固定宽度，规格自适应
- **优势**：响应式友好

### 字体设计

#### 规格名称
- **字体大小**：12px（适中清晰）
- **字体粗细**：600（加粗突出）
- **颜色**：#333（深灰色）

#### 价格信息
- **字体大小**：11px（稍小但清晰）
- **字体粗细**：500（中等粗细）
- **颜色**：#e60012（品牌红色）

## 用户体验提升

### ✨ **信息获取效率**
- **一眼扫描**：规格和价格在同一行
- **快速对比**：不同尺寸的信息易于对比
- **决策便捷**：关键信息集中展示

### 🎯 **视觉层次**
- **主要信息**：尺寸图片占主导
- **次要信息**：规格和价格并排辅助
- **层次清晰**：从图片到文字的自然流程

### 📐 **空间优化**
- **垂直空间节省**：减少卡片高度
- **水平空间利用**：充分利用卡片宽度
- **整体协调**：与淋酱选择区域平衡

## 适配考虑

### 不同尺寸文字
- **短文字**：9英寸、12英寸等
- **长文字**：可能的其他规格名称
- **自适应**：flex: 1 自动调整

### 不同价格长度
- **短价格**：¥39
- **长价格**：¥99.9、优惠价格等
- **防换行**：white-space: nowrap 保证单行

### 移动端适配
- **触摸友好**：保持足够的点击区域
- **字体清晰**：12px/11px 在移动端清晰可读
- **间距适中**：8px gap 适合触摸操作

## 最终效果

现在的尺寸选择卡片：
- ✅ **图片占满容器**：视觉饱满的尺寸对比图
- ✅ **信息并排显示**：规格名称 + 价格在同一行
- ✅ **空间利用高效**：紧凑而不拥挤的布局
- ✅ **视觉层次清晰**：图片主导，文字辅助
- ✅ **响应式友好**：适配不同屏幕尺寸

完全符合您的要求，提供了更高效的信息展示和更好的用户体验！
