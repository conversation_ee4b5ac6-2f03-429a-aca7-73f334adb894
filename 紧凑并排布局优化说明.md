# 紧凑并排布局优化说明

## 优化内容

将尺寸选择和淋酱选择改为并排显示，并大幅缩小尺寸，实现更紧凑的布局。

## 布局变化

### 修改前（垂直布局，尺寸较大）
```
尺寸选择：
┌─────────────┐  ┌─────────────┐
│  [大饼图]   │  │  [大饼图]   │
│    9英寸    │  │   12英寸    │
│   (价格)    │  │   (价格)    │
└─────────────┘  └─────────────┘

淋酱选择：
┌─────────┐ ┌─────────┐ ┌─────────┐
│ [大图标] │ │ [大图标] │ │ [大图标] │
│  酱料名  │ │  酱料名  │ │  酱料名  │
│  描述   │ │  描述   │ │  描述   │
└─────────┘ └─────────┘ └─────────┘
```

### 修改后（并排布局，紧凑尺寸）
```
┌─────────────────┬─────────────────┐
│ 尺寸            │ 淋酱            │
│ [小饼图][小饼图] │ [小图标][小图标] │
│ 9英寸   12英寸  │ 番茄酱  白酱    │
└─────────────────┴─────────────────┘
```

## 尺寸优化对比

### 饼图图标
- **SVG尺寸**：50x50px → 30x30px
- **中心标签**：24x24px → 16x16px
- **字体大小**：14px → 10px

### 卡片尺寸
- **最小宽度**：120px → 70px
- **内边距**：15px → 8px
- **边框圆角**：12px → 8px

### 酱料图标
- **图片尺寸**：40x40px → 24x24px
- **最小宽度**：80px → 50px
- **内边距**：10px → 6px

### 字体大小
- **尺寸名称**：14px → 11px
- **价格文字**：12px → 9px
- **酱料名称**：12px → 9px
- **酱料描述**：10px → 8px

## 技术实现

### 并排布局
```scss
.size-sauce-row {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;

  .size-selection,
  .sauce-selection {
    flex: 1; // 各占一半宽度
    margin-bottom: 0;
  }
}
```

### 紧凑尺寸选择
```scss
.size-option {
  border: 1px solid #e0e0e0; // 细边框
  border-radius: 8px; // 小圆角
  padding: 8px; // 小内边距
  min-width: 70px; // 小宽度
  flex: 1; // 自适应

  .pizza-chart {
    .chart-svg {
      width: 30px; // 小饼图
      height: 30px;
    }

    .size-label {
      width: 16px; // 小标签
      height: 16px;
      font-size: 10px; // 小字体
    }
  }
}
```

### 紧凑酱料选择
```scss
.sauce-option {
  padding: 6px; // 更小内边距
  min-width: 50px; // 更小宽度
  flex: 1; // 自适应

  .sauce-image {
    width: 24px; // 小图标
    height: 24px;
    padding: 2px; // 小内边距
  }

  .sauce-name {
    font-size: 9px; // 小字体
  }

  .sauce-desc {
    font-size: 8px; // 更小字体
  }
}
```

## 优化效果

### 🎯 **空间利用**
- **横向布局**：充分利用横向空间
- **紧凑设计**：减少垂直空间占用
- **信息密度**：在有限空间内展示更多信息

### 📱 **视觉效果**
- **比例协调**：与整体布局更协调
- **层次清晰**：保持清晰的信息层次
- **美观简洁**：精致的小尺寸设计

### 🎨 **用户体验**
- **快速浏览**：一眼看到所有选项
- **操作便捷**：相关选择在同一行
- **视觉平衡**：不抢夺主要内容焦点

## 响应式考虑

### 弹性布局
- `flex: 1` 让选项自适应宽度
- 最小宽度保证内容完整显示
- 间距适中，避免拥挤

### 可读性保证
- 字体大小保持可读性下限
- 图标尺寸保持识别性
- 点击区域足够大

### 移动端适配
- 紧凑设计适合小屏幕
- 触摸友好的交互区域
- 清晰的视觉反馈

## 设计原则

### 信息层次
- 保持饼图的直观性
- 酱料图标的识别性
- 文字信息的可读性

### 视觉平衡
- 尺寸和酱料区域平衡
- 与商品图片协调
- 整体布局和谐

### 交互体验
- 保持足够的点击区域
- 清晰的选中状态
- 平滑的过渡效果

现在的布局更加紧凑高效，在保持美观和功能性的同时，大大节省了空间！
