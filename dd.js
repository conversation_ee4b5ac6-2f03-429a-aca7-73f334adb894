
name: "",
    components: {
    "dl-modal": L,
        Stepper: y["a"]
},
data: function() {
    return {
        ModalOpen: !0,
        ModalWidth: "86%",
        ModalHeight: "90%",
        productNum: 1,
        doublePizzaList: [],
        doubleFlavorCode: "",
        selectSizeCode: "",
        selectSauceInfo: {},
        productInfo: {
            productName: "加载中...",
            description: "",
            productImg: i("9328"),
            sizeList: [],
            sauceList: [],
            pastryList: [],
            baseIngredientsList: [],
            additionalIngredientsList: [],
            pastryMap: {},
            sauceSelectNumber: 0
        },
        productResource: {},
        MinModalWidth: "900px",
        burdenNum: 0,
        pizzaPrice: "",
        selectPastryInfo: "",
        ingredientList: "",
        ruleNameList: [],
        drenchSauceList: [],
        drenchSauceMap: {},
        menuActivityPrice: 0,
        sauceCountPrice: 0,
        additionalIngrCountPrice: 0,
        baseIngrCountPrice: 0,
        ingCombosCountPrice: 0,
        showLinePrice: !1,
        bigPastryImageInfo: []
    }
},
computed: {
    TranStyle: function() {
        var t, e, i = this.ModalWidth, o = this.ModalHeight;
        if (i.includes("%") ? (i = i,
            t = -i.replace("%", "") / 2 + "%") : (t = -i / 2 + "px",
            i += "px"),
            o.includes("%")) {
            var n = document.body.clientHeight;
            o = n * o.replace("%", "") / 100,
                e = -o / 2 + "px",
                o += "px"
        } else
            e = -o / 2 + "px",
                o += "px";
        return {
            width: i,
            marginLeft: t,
            marginTop: e
        }
    },
    lang: function() {
        return this.$i18n.locale
    },
    ingrCount: function() {
        if (!this.selectSizeCode)
            return 0;
        var t = this.productInfo.baseIngredientsList
            , e = this.productInfo.additionalIngredientsList
            , i = t.reduce((function(t, e) {
                return t + e.buyNum
            }
        ), 0)
            , o = e.reduce((function(t, e) {
                return t + e.buyNum
            }
        ), 0);
        return i + o
    }
},
props: {
    productCode: {
        type: [String, Object],
    default: function() {
            return "f79e5c8b80de4d6ba147fa1e6f041e53"
        }
    }
},
mounted: function() {
    this.GetPizzaDetail()
},
methods: {
    stepperVal: function() {
        var t = this
            , e = 0
            , i = 0;
        this.productInfo.baseIngredientsList.map((function(i) {
                if (i.buyNum > 0)
                    1 != i.ingredientType || t.drenchSauceMap[i.code] || (t.drenchSauceMap[i.code] = !0,
                        t.drenchSauceList.push(i));
                else if (1 == i.ingredientType && t.drenchSauceMap[i.code]) {
                    var o = t.drenchSauceList.findIndex((function(t) {
                            return t.code == i.code
                        }
                    ));
                    t.drenchSauceList.splice(o, 1),
                        delete t.drenchSauceMap[i.code]
                }
                if (i.buyNum > 1)
                    return e += i.buyNum - 1
            }
        )),
            this.productInfo.additionalIngredientsList.map((function(e) {
                    if (e.buyNum > 0)
                        return 1 != e.ingredientType || t.drenchSauceMap[e.code] || (t.drenchSauceMap[e.code] = !0,
                            t.drenchSauceList.push(e)),
                            i += e.buyNum - 0;
                    if (1 == e.ingredientType && t.drenchSauceMap[e.code]) {
                        var o = t.drenchSauceList.findIndex((function(t) {
                                return t.code == e.code
                            }
                        ));
                        t.drenchSauceList.splice(o, 1),
                            delete t.drenchSauceMap[e.code]
                    }
                }
            )),
            this.burdenNum = e + i,
            this.countPriceChange(),
            this.CDP("ingredientList")
    },
    ModalClose: function() {
        this.$emit("ModalClose", 1)
    },
    ingredientChange: function() {
        var t = []
            , e = []
            , i = ""
            , o = "";
        return i = this.productInfo.baseIngredientsList.reduce((function(e, i) {
                return t.push(i.name),
                i.buyNum + e
            }
        ), 0),
            o = this.productInfo.additionalIngredientsList.reduce((function(t, i) {
                    return i.buyNum > 0 && e.push(i.name),
                    i.buyNum + t
                }
            ), 0),
            {
                c_ingredient: t,
                c_ingredientnum: i,
                c_addingredient: e,
                c_addingredientnum: o
            }
    },
    GetPizzaDetail: function() {
        var t = this;
        this.drenchSauceList = [],
            f["a"].dispatch("productDetail", this.productCode).then((function(e) {
                    t.loadshow = !1;
                    var i = e
                        , o = i.description && i.description.split("[br]").reduce((function(t, e) {
                            return e.match(/^配料/) ? t["ingredient"] = e : e.match(/^规格/) ? t["spec"] = e : t["description"] = e,
                                t
                        }
                    ), {})
                        , n = i.pastryList.reduce((function(t, e, i) {
                            return e.index = i,
                                t[e.code] = e,
                                t
                        }
                    ), {})
                        , r = i.productList.reduce((function(t, e) {
                            return (t[e.sizeCode] = t[e.sizeCode] || []).push(Object.assign({}, e, n[e.pastryCode])),
                                t[e.sizeCode] = t[e.sizeCode].sort((function(t, e) {
                                        return t.index - e.index
                                    }
                                )),
                                t
                        }
                    ), {})
                        , s = i.sizeList.reduce((function(t, e) {
                            return t[e.code] = e,
                                t
                        }
                    ), {})
                        , c = i.sauceList.map((function(t) {
                            return t.productPrice = 0,
                                t.isDefault ? t.isDefault = 1 : t.isDefault = 0,
                                t.saucesImage = t.imageURL2 || t.imageURL1,
                                t
                        }
                    ))
                        , a = c.filter((function(t) {
                            return 1 == t.isDefault
                        }
                    ))
                        , u = a.length || 0;
                    t.bigPastryImageInfo = i.bigPastryImageInfo || [],
                        t.productInfo = {
                            sauceSelectNumber: u,
                            descriptionInfo: o,
                            productName: i.productName,
                            productNameEN: i.productNameEN,
                            FreeShippingDesc: i.FreeShippingDesc || "",
                            FreeShippingDescEN: i.FreeShippingDescEN || "",
                            iconNum: i.iconNum || "",
                            iconUrl: i.iconUrl || "",
                            sizeAdsDescNum: i.sizeAdsDescNum || "",
                            sizeAdsDescRemark: i.sizeAdsDescRemark || "",
                            sizeAdsDescUrl: i.sizeAdsDescUrl || "",
                            crustAdsDescNum: i.crustAdsDescNum || "",
                            crustAdsDescRemark: i.crustAdsDescRemark || "",
                            crustAdsDescUrl: i.crustAdsDescUrl || "",
                            description: i.description,
                            description2: i.description2,
                            descriptionEN: i.descriptionEN || i.description,
                            descriptionEN2: i.descriptionEN2 || i.description2,
                            productImg: i.detailPageProImgUrl || i.imageURL2,
                            tagRemark: i.tagRemark,
                            tagDescUrl: i.tagDescUrl,
                            sizeList: i.productList.reduce((function(t, e) {
                                    var i = t.filter((function(t) {
                                            return t.code === e.sizeCode
                                        }
                                    )).length;
                                    return i || t.push(s[e.sizeCode]),
                                        t
                                }
                            ), []),
                            sauceList: c,
                            pastryList: i.pastryList,
                            sellingPrice: i.sellingPrice,
                            ingCombos: (i.ingCombos || []).map((function(t) {
                                    return t.ingList = (t.ingList || []).map((function(t) {
                                            return t.imageUrl = t.imageURL2 || t.imageURL1,
                                                t.isDefault = t.isDefault ? 1 : 0,
                                                t.buyNum = t.isDefault ? t.initQuantity || 1 : 0,
                                                t.freeQuantity = t.freeQuantity || 0,
                                                t.isAdditional = 0 === t.baseFlag,
                                                t
                                        }
                                    )),
                                        t
                                }
                            )),
                            isShowCouponPrice: i.isShowCouponPrice,
                            baseIngredientsList: i.baseIngredientList ? i.baseIngredientList.map((function(t) {
                                    return t.isDefault = 1,
                                        t.buyNum = 1,
                                        t
                                }
                            )) : [],
                            additionalIngredientsList: i.additionalIngredientList ? i.additionalIngredientList.map((function(t) {
                                    return t.isDefault = 0,
                                        t.buyNum = 0,
                                        t
                                }
                            )) : [],
                            pastryMap: r,
                            productList: i.productList,
                            productCode: i.productCode,
                            detailImageURLs: i.detailImageURLs && i.detailImageURLs.split(";")
                        },
                        t.productInfo.sizeList = t.productInfo.sizeList.sort((function(t, e) {
                                return t.sort - e.sort
                            }
                        )),
                        console.log("pastryList;;;;;;;;;;;;;;;;;"),
                        console.log(i.pastryList),
                        console.log("11111;;;;;;;;;;;;;;;;;;;;;;"),
                        console.log(r),
                        t.productInfo.baseIngredientsList.concat(t.productInfo.additionalIngredientsList).forEach((function(e) {
                                1 === e.ingredientType && e.buyNum > 0 && !t.drenchSauceMap[e.code] && (t.drenchSauceMap[e.code] = !0,
                                    t.drenchSauceList.push(e))
                            }
                        ));
                    var d = ""
                        , p = "";
                    i.sizeList && i.sizeList.map((function(t) {
                            i.productList.map((function(e, i) {
                                    e.isDefault && e.sizeCode == t.code && (d = t.code,
                                        p = e.pastryCode)
                                }
                            ))
                        }
                    )),
                        t.selectSizeCode = d,
                        t.productResource = i;
                    var f = [];
                    t.selectPastryInfo = r[d].find((function(t) {
                            return t.code == p
                        }
                    )) || r[d][0],
                    i.sauceList && (f = i.sauceList.find((function(t) {
                            return 1 == t.isDefault
                        }
                    )) || i.sauceList[0]),
                        t.selectSauceInfo = f,
                        t.isLoading = !1,
                    t.afterInnerShow && t.renderFns.pop()(),
                        t.$nextTick((function() {
                                var t = document.querySelector(".PizaaTopFixed") && document.querySelector(".PizaaTopFixed").offsetHeight;
                                document.querySelector(".chooseItem") && (document.querySelector(".chooseItem").style.marginTop = t + "px")
                            }
                        )),
                        t.countPriceChange();
                    var h = JSON.parse(JSON.stringify(Object.assign(Object.keys(t.productResource).reduce((function(e, i) {
                            return "object" !== N(t.productResource[i]) && (e[i] = t.productResource[i]),
                                e
                        }
                    ), {}), {
                        sizeCode: t.selectSizeCode,
                        pastryInfo: t.selectPastryInfo,
                        selectSauceInfo: t.selectSauceInfo,
                        baseIngredientsList: t.productInfo.baseIngredientsList,
                        additionalIngredientsList: t.productInfo.additionalIngredientsList,
                        productImg: t.productInfo.productImg,
                        countPrice: t.pizzaPrice,
                        unitCustomPrice: t.pizzaPrice,
                        productNum: t.productNum,
                        sauceList: t.productInfo.sauceList,
                        ingCombos: t.productInfo.ingCombos || [],
                        xxx: "123",
                        productCode: t.productCode,
                        productGroupCode: t.productCode,
                        flavorList: t.productResource.flavorList,
                        pizzaSizeName: t.productResource.sizeList.filter((function(e) {
                                return e.code == t.selectSizeCode
                            }
                        ))
                    })))
                        , m = [];
                    m.push(l["a"].itemType1(h));
                    try {
                        t.CDP("ingredientList")
                    } catch (g) {}
                }
            )).catch((function(e) {
                    t.$toast({
                        type: "text",
                        message: e + "～获取比萨详情",
                        duration: 2e3
                    })
                }
            ))
    },
    changeSauce: function(t) {
        var e = this.productInfo
            , i = e.sauceSelectNumber
            , o = e.sauceList;
        console.log(i, o.length),
        i === o.length || (1 === i ? o.forEach((function(e) {
                e.code === t.code ? e.isDefault = 1 : e.isDefault = 0
            }
        )) : t.isDefault ? t.isDefault = 0 : o.filter((function(t) {
                return t.isDefault
            }
        )).length >= i ? this.$toast({
            type: "text",
            message: "最多选择".concat(i, "种，请取消后选择"),
            duration: 2e3
        }) : t.isDefault = 1)
    },
    CDP: function(t) {
        var e = this.pizzaPrice
            , i = this.productNum
            , o = this.productInfo.productName
            , n = this.productInfo.productCode
            , r = this.selectSizeCode
            , s = this.selectPastryInfo
            , c = this.selectSauceInfo
            , a = ""
            , u = "c_scan_product";
        "ingredientList" == t ? (this.ingredientList = this.ingredientChange(),
            a = this.ingredientList) : "shopping" == t && (u = "c_add_to_cart");
        try {
            clab_tracker.ready((function() {
                    this.track(u, {
                        c_proName: o,
                        c_proId: n,
                        c_type: "披萨",
                        c_prize: e,
                        c_size: r,
                        c_bottom: s.name,
                        c_sause: c.name,
                        c_ingredient: a.c_ingredient,
                        c_ingredientnum: a.c_ingredientnum,
                        c_addingredient: a.c_addingredient,
                        c_addingredientnum: a.c_addingredientnum,
                        c_staple: "",
                        c_snack: "",
                        c_drink: "",
                        c_quantity: i
                    })
                }
            ))
        } catch (d) {}
    },
    sizeListChange: function(t) {
        var e = this;
        this.selectSizeCode = t.code;
        var i = "";
        this.productInfo.sizeList && this.productInfo.sizeList.map((function(t) {
                e.productInfo.productList.map((function(e, o) {
                        e.isDefault && e.sizeCode == t.code && (i = e.pastryCode)
                    }
                ))
            }
        )),
            this.selectPastryInfo = this.productInfo.pastryMap[this.selectSizeCode].find((function(t) {
                    return t.code == i
                }
            )) || this.productInfo.pastryMap[this.selectSizeCode][0],
            this.countPriceChange(),
            this.CDP()
    },
    countPriceChange: function(t, e) {
        t && (this.selectPastryInfo = t,
            this.CDP()),
            console.log("selectPastryInfo", this.selectPastryInfo);
        var i = this.selectPastryInfo.sellingPrice
            , o = this.selectPastryInfo.couponPrice
            , n = this.selectSizeCode
            , r = this.productInfo.baseIngredientsList
            , s = this.productInfo.additionalIngredientsList
            , c = (this.productInfo.sauceList || []).reduce((function(t, e) {
                return e.isDefault && (t += (e.priceList.find((function(t) {
                        return t.sizeCode === n
                    }
                )) || {
                    sellingPrice: 0
                }).sellingPrice),
                    t
            }
        ), 0);
        this.sauceCountPrice = c;
        var a = this.productInfo.ingCombos.reduce((function(t, e) {
                return t.concat(e.ingList)
            }
        ), [])
            , u = a.reduce((function(t, e) {
                if (e.selectPrice = 0,
                e.priceList && e.priceList.length > 0) {
                    var i = e.priceList.find((function(t) {
                            return t.sizeCode === n
                        }
                    )) || {
                        sellingPrice: 0
                    };
                    e.selectPrice = e.priceList.length ? i.sellingPrice * (e.buyNum > e.freeQuantity ? e.buyNum - e.freeQuantity : 0) : 0,
                        e.originalPrice = i.sellingPrice
                }
                return e.selectPrice + t
            }
        ), 0) || 0;
        this.ingCombosCountPrice = u;
        var d = r.reduce((function(t, e) {
                if (e.selectPrice = 0,
                e.priceList && e.priceList.length > 0) {
                    var i = e.priceList.find((function(t) {
                            return t.sizeCode === n
                        }
                    )) || {
                        sellingPrice: 0
                    };
                    e.buyNum > 1 && (e.selectPrice = (e.buyNum - 1) * i.sellingPrice),
                        e.originalPrice = i.sellingPrice
                }
                return e.selectPrice + t
            }
        ), 0) || 0;
        this.baseIngrCountPrice = d;
        var l = s.reduce((function(t, e) {
                if (e.selectPrice = 0,
                e.priceList && e.priceList.length > 0) {
                    var i = e.priceList.find((function(t) {
                            return t.sizeCode === n
                        }
                    )) || {
                        sellingPrice: 0
                    };
                    e.buyNum > 0 && (e.selectPrice = (e.buyNum - 0) * i.sellingPrice)
                }
                return e.selectPrice + t
            }
        ), 0) || 0;
        this.additionalIngrCountPrice = l,
            o ? (this.pizzaPrice = (this.productNum * (i + c + l + u + d)).toFixed(2),
                this.couponPrice = (this.productNum * (o + c + l + u + d)).toFixed(2)) : (this.pizzaPrice = (this.productNum * (i + c + l + u + d)).toFixed(2),
                this.couponPrice = 0),
            this.getRuleActivity()
    },
    buyPizza: function() {
        var t = this
            , e = this.productInfo
            , i = e.sauceSelectNumber
            , o = e.sauceList;
        if (o.filter((function(t) {
                return t.isDefault
            }
        )).length < i)
            return this.$toast({
                type: "text",
                message: "至少选择".concat(i, "种底酱"),
                duration: 2e3
            });
        console.log("productInfoAddCart;;;;;;;"),
            console.log(this.productInfo),
            this.$emit("buy", JSON.parse(JSON.stringify(Object.assign(Object.keys(this.productResource).reduce((function(e, i) {
                    return "object" !== N(t.productResource[i]) && (e[i] = t.productResource[i]),
                        e
                }
            ), {}), {
                sizeCode: this.selectSizeCode,
                pastryInfo: this.selectPastryInfo,
                selectSauceInfo: this.selectSauceInfo,
                baseIngredientsList: this.productInfo.baseIngredientsList,
                additionalIngredientsList: this.productInfo.additionalIngredientsList,
                sauceList: this.productInfo.sauceList,
                productImg: this.productInfo.productImg,
                countPrice: this.pizzaPrice,
                unitCustomPrice: this.pizzaPrice,
                productNum: this.productNum,
                ingCombos: this.productInfo.ingCombos || [],
                productCode: this.productCode,
                productGroupCode: this.productCode,
                flavorList: this.productResource.flavorList,
                pizzaSizeName: this.productResource.sizeList.filter((function(e) {
                        return e.code == t.selectSizeCode
                    }
                ))
            })))),
            this.CDP("shopping")
    },
    getSelectProductInfo: function() {
        var t = this;
        return JSON.parse(JSON.stringify(Object.assign(Object.keys(this.productResource).reduce((function(e, i) {
                return "object" !== N(t.productResource[i]) && (e[i] = t.productResource[i]),
                    e
            }
        ), {}), {
            sizeCode: this.selectSizeCode,
            pastryInfo: this.selectPastryInfo,
            selectSauceInfo: this.selectSauceInfo,
            baseIngredientsList: this.productInfo.baseIngredientsList,
            additionalIngredientsList: this.productInfo.additionalIngredientsList,
            productImg: this.productInfo.productImg,
            countPrice: this.pizzaPrice,
            unitCustomPrice: this.pizzaPrice,
            sauceList: this.productInfo.sauceList,
            productNum: this.productNum,
            ingCombos: this.productInfo.ingCombos || [],
            ccc: 123,
            productCode: this.productCode,
            productGroupCode: this.productCode,
            flavorList: this.productResource.flavorList,
            pizzaSizeName: this.productResource.sizeList.filter((function(e) {
                    return e.code == t.selectSizeCode
                }
            ))
        })))
    },
    getRuleActivity: function() {
        var t = this
            , e = [];
        e.push(l["a"].itemType1(this.getSelectProductInfo())),
        this.productInfo.isShowCouponPrice || this.$post("matchingRuleActivity", {
            items: e,
            orderTime: Date.now()
        }).then((function(e) {
                if (t.$dominosloading.hide(),
                    e) {
                    t.ruleNameList = e.data && e.data.data || [],
                        t.ruleNameList.forEach((function(t) {
                                t.focus = !1
                            }
                        ));
                    var i = t.ruleNameList.find((function(t) {
                            if (t.canChoice && t.menuActivityPrice)
                                return t
                        }
                    ));
                    i ? t.handleSelect(i) : (t.showLinePrice = !1,
                        t.menuActivityPrice = 0),
                        t.menuActivityPrice && t.menuActivityPrice != t.pizzaPrice ? (t.couponPrice = t.menuActivityPrice,
                            t.showLinePrice = !0) : t.showLinePrice = !1
                } else
                    t.ruleNameList = [],
                        t.menuActivityPrice = 0;
                t.menuActivityPrice && t.showLinePrice && (t.menuActivityPrice = t.menuActivityPrice.toFixed(2))
            }
        ), (function(e) {
                Toast(e),
                    t.activityList = [],
                    t.menuActivityPrice = 0
            }
        ))
    },
    handleSelect: function(t) {
        t.canChoice && (this.menuActivityPrice = t.menuActivityPrice,
            this.ruleNameList.map((function(e) {
                    return e.ruleId == t.ruleId ? e.focus = !0 : e.focus = !1,
                        e
                }
            )),
            this.menuActivityPrice && this.menuActivityPrice != this.pizzaPrice ? (this.couponPrice = this.menuActivityPrice,
                this.showLinePrice = !0) : this.showLinePrice = !1)
    },
    changePractice: function(t, e) {
        var i = t.ingList.reduce((function(t, e) {
                return t + e.buyNum
            }
        ), 0);
        t.minSelectable === t.maxSelectable && 1 === t.minSelectable ? (t.ingList.forEach((function(t) {
                t.buyNum = 0
            }
        )),
            e.buyNum = 1) : t.maxSelectable > i && 0 === e.buyNum ? e.buyNum = 1 : e.buyNum = 0,
            this.countPriceChange()
    }
}
}