<template>
    <div class="order-create-page">
        <!-- 配送信息和一键识别区域 -->
        <el-row :gutter="20" class="info-section">
            <!-- 自定义填写下单信息区域 -->
            <el-col :xs="24" :lg="14">
                <el-card class="order-info-card" shadow="hover">
                    <template #header>
                        <div class="card-header">
                            <el-icon class="header-icon"><Location /></el-icon>
                            <span>配送信息</span>
                        </div>
                    </template>

                    <el-form :model="orderForm" :rules="orderRules" ref="orderFormRef" label-width="100px">
                        <el-row :gutter="20">
                            <el-col :xs="24" :sm="12">
                                <el-form-item label="送餐城市:" prop="city">
                                    <el-select v-model="orderForm.city" placeholder="请选择城市" style="width: 100%">
                                        <el-option label="上海市" value="shanghai" />
                                        <el-option label="北京市" value="beijing" />
                                        <el-option label="广州市" value="guangzhou" />
                                        <el-option label="深圳市" value="shenzhen" />
                                        <el-option label="杭州市" value="hangzhou" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="12">
                                <el-form-item label="送餐门店:" prop="store">
                                    <el-select v-model="orderForm.store" placeholder="请选择门店" style="width: 100%">
                                        <el-option label="上海静安店" value="shanghai_jingan" />
                                        <el-option label="上海徐汇店" value="shanghai_xuhui" />
                                        <el-option label="上海浦东店" value="shanghai_pudong" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :xs="24">
                                <el-form-item label="送餐地址:" prop="address">
                                    <el-input
                                        v-model="orderForm.address"
                                        placeholder="请输入小区/写字楼/学校名称"
                                        style="width: calc(100% - 80px); margin-right: 10px;"
                                    />
                                    <el-button type="primary" @click="searchAddress">搜索</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :xs="24">
                                <el-form-item label="详细地址:" prop="detailAddress">
                                    <el-input
                                        v-model="orderForm.detailAddress"
                                        placeholder="楼栋门牌号，例：1号101室"
                                        style="width: 100%;"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <div class="address-warning" v-if="orderForm.address">
                            <el-alert
                                title="如果写地址和所在城市不匹配，则无法下订单"
                                type="warning"
                                :closable="false"
                                show-icon
                            />
                        </div>

                        <el-row :gutter="20" style="margin-top: 20px;">
                            <el-col :xs="24" :sm="12">
                                <el-form-item label="收餐人:" prop="recipient">
                                    <el-input v-model="orderForm.recipient" placeholder="请输入收餐人姓名" />
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="12">
                                <el-form-item label="联系电话:" prop="phone">
                                    <el-input v-model="orderForm.phone" placeholder="请输入联系电话" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-card>
            </el-col>

            <!-- 一键识别收件信息区域 -->
            <el-col :xs="24" :lg="10">
                <el-card class="recognition-card" shadow="hover">
                    <template #header>
                        <div class="card-header">
                            <el-icon class="header-icon"><MagicStick /></el-icon>
                            <span>一键识别收件信息</span>
                        </div>
                    </template>

                    <div class="recognition-content">
                        <p class="recognition-tip">
                            粘贴收件人姓名、手机号、收货地址(需包含省市区)，可快速识别您的收货信息。
                        </p>

                        <el-input
                            v-model="recognitionText"
                            type="textarea"
                            :rows="6"
                            placeholder="请粘贴收件信息，例如：张三 13800138000 上海市静安区南京西路123号"
                            class="recognition-textarea"
                        />

                        <div class="recognition-actions">
                            <el-button @click="clearRecognition" type="info" plain>清除</el-button>
                            <el-button @click="recognizeInfo" type="primary" :loading="recognizing">
                                <el-icon><MagicStick /></el-icon>
                                一键识别
                            </el-button>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 菜单商品展示区域 -->
        <el-card class="menu-card" shadow="hover">
            <template #header>
                <div class="card-header">
                    <el-icon class="header-icon"><Food /></el-icon>
                    <span>菜单商品</span>
                </div>
            </template>

            <div class="menu-content">
                <!-- 搜索框 -->
                <div class="menu-search">
                    <el-input
                        v-model="searchKeyword"
                        placeholder="搜索商品..."
                        :prefix-icon="Search"
                        size="large"
                        class="search-input"
                        @input="filterProducts"
                    />
                </div>

                <!-- 一级分类标签 -->
                <div class="category-tabs">
                    <el-button
                        v-for="category in categories"
                        :key="category.value"
                        :type="activeCategory === category.value ? 'danger' : 'default'"
                        :plain="activeCategory !== category.value"
                        @click="switchMainCategory(category)"
                        class="category-btn"
                    >
                        {{ category.label }}
                    </el-button>
                </div>

                <!-- 二级分类标签 -->
                <div class="sub-category-tabs" v-if="subCategories.length > 0">
                    <el-button
                        v-for="subCategory in subCategories"
                        :key="subCategory.value"
                        :type="activeCategory === subCategory.value ? 'danger' : 'default'"
                        :plain="activeCategory !== subCategory.value"
                        @click="switchCategory(subCategory.value)"
                        class="sub-category-btn"
                        size="small"
                    >
                        {{ subCategory.label }}
                    </el-button>
                </div>

                <!-- 商品列表 -->
                <div v-loading="loading" class="products-grid">
                    <div
                        v-for="product in filteredProducts"
                        :key="product.productCode"
                        class="product-card"
                    >
                        <!-- 商品图片 -->
                        <div class="product-image">
                            <img
                                :src="product.imageURL1"
                                :alt="product.productName"
                                @error="handleImageError"
                            />
                        </div>

                        <!-- 商品信息 -->
                        <div class="product-info">
                            <!-- 价格区间 -->
                            <div class="product-price">
                                <span v-if="product.menuListPriceList && product.menuListPriceList.length > 1">
                                    {{ product.menuListPriceList[0].exceptionText }} - {{ product.menuListPriceList[product.menuListPriceList.length - 1].exceptionText }}
                                </span>
                                <span v-else-if="product.menuListPriceList && product.menuListPriceList.length === 1">
                                    {{ product.menuListPriceList[0].exceptionText }}
                                </span>
                                <span v-else>价格待定</span>
                            </div>

                            <!-- 商品名称 -->
                            <div class="product-name">{{ product.productName }}</div>

                            <!-- 商品标签 -->
                            <div class="product-badges">
                                <div
                                    v-for="tag in getProductTags(product)"
                                    :key="tag"
                                    class="product-badge"
                                    :class="{ 'hot-badge': tag === '热门' }"
                                >
                                    {{ tag }}
                                </div>
                            </div>

                            <!-- 商品描述 -->
                            <div class="product-desc">{{ product.description }}</div>

                            <!-- 规格价格列表 -->
                            <div v-if="product.menuListPriceList && product.menuListPriceList.length > 1" class="size-price-list">
                                <div class="size-options">
                                    <span
                                        v-for="priceItem in product.menuListPriceList"
                                        :key="priceItem.exceptionKey"
                                        class="size-option"
                                    >
                                        <span class="size-name">{{ priceItem.exceptionKey }}</span>
                                        <span class="size-price">{{ priceItem.exceptionText }}</span>
                                    </span>
                                </div>
                            </div>

                            <!-- 加入购物车按钮 -->
                            <el-button
                                type="danger"
                                size="large"
                                class="add-to-cart-btn"
                                @click="addToCart(product)"
                            >
                                <span v-if="product.menuListPriceList && product.menuListPriceList.length > 1">
                                    选择规格
                                </span>
                                <span v-else>
                                    加入订单
                                </span>
                            </el-button>
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <el-empty v-if="filteredProducts.length === 0" description="暂无商品" />
            </div>
        </el-card>

        <!-- 购物车悬浮按钮 -->
        <div class="cart-float" v-if="cartItems.length > 0" @click="showCart = true">
            <el-badge :value="cartItems.length" class="cart-badge">
                <el-button type="danger" size="large" circle>
                    <el-icon size="20"><ShoppingCart /></el-icon>
                </el-button>
            </el-badge>
        </div>

        <!-- 商品详情弹窗 -->
        <ProductDetailDialog
            v-model:visible="detailDialogVisible"
            :product="currentDetailProduct"
            @add-to-cart="handleAddToCart"
        />

        <!-- 购物车抽屉 -->
        <el-drawer v-model="showCart" title="购物车" size="400px">
            <div class="cart-content">
                <div v-if="cartItems.length === 0" class="empty-cart">
                    <el-empty description="购物车为空" />
                </div>
                <div v-else>
                    <div v-for="item in cartItems" :key="item.cartKey" class="cart-item">
                        <img
                            :src="item.imageURL1"
                            :alt="item.productName"
                            class="cart-item-image"
                            @error="handleImageError"
                        />
                        <div class="cart-item-info">
                            <div class="cart-item-name">
                                {{ item.productName }}
                                <span v-if="item.selectedSize" class="cart-item-size">({{ item.selectedSize }})</span>
                            </div>
                            <div class="cart-item-price">{{ getProductPriceText(item) }}</div>
                        </div>
                        <div class="cart-item-actions">
                            <el-input-number
                                v-model="item.quantity"
                                :min="1"
                                size="small"
                                @change="updateCartItem(item)"
                            />
                            <el-button
                                type="danger"
                                text
                                @click="removeFromCart(item.cartKey)"
                                style="margin-left: 10px;"
                            >
                                删除
                            </el-button>
                        </div>
                    </div>
                    <div class="cart-total">
                        <div class="total-price">总计: ¥{{ totalPrice }}</div>
                        <el-button type="danger" size="large" style="width: 100%; margin-top: 10px;" @click="submitOrder">
                            提交订单
                        </el-button>
                    </div>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
    ShoppingCart,
    Location,
    MagicStick,
    Food,
    Search
} from '@element-plus/icons-vue'
import ProductDetailDialog from '/@/components/ProductDetailDialog.vue'
import { getProducts, getProductDetail as apiGetProductDetail, getCategories } from '/@/api/mce/product'

defineOptions({
    name: 'orderCreate',
})

// 表单数据
const orderForm = reactive({
    city: '',
    address: '',
    detailAddress: '',
    recipient: '',
    phone: '',
    store: ''
})

// 表单验证规则
const orderRules = {
    city: [{ required: true, message: '请选择送餐城市', trigger: 'change' }],
    address: [{ required: true, message: '请输入送餐地址', trigger: 'blur' }],
    recipient: [{ required: true, message: '请输入收餐人姓名', trigger: 'blur' }],
    phone: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    store: [{ required: true, message: '请选择送餐门店', trigger: 'change' }]
}

const orderFormRef = ref()

// 一键识别相关
const recognitionText = ref('')
const recognizing = ref(false)

// 商品相关
const searchKeyword = ref('')
const activeCategory = ref('all')
const showCart = ref(false)
const cartItems = ref<any[]>([])

// 分类数据 - 从API获取
const categories = ref<any[]>([
    { label: '全部', value: 'all' } // 默认添加"全部"选项
])

// 原始分类数据
const originalCategoriesData = ref<any[]>([])

// 二级分类数据
const subCategories = ref<any[]>([])

// 当前选中的一级分类
const selectedMainCategory = ref<any>(null)

// 商品数据 - 从API获取
const allProducts = ref<any[]>([]) // 所有商品数据
const products = ref<any[]>([]) // 当前显示的商品数据
const loading = ref(false)


// 计算属性 - 现在直接返回products，因为筛选在API层面完成
const filteredProducts = computed(() => {
    return products.value
})

const totalPrice = computed(() => {
    return cartItems.value.reduce((total, item) => {
        const price = getProductPrice(item)
        return total + (price * item.quantity)
    }, 0).toFixed(2)
})

// 获取商品价格的辅助函数
const getProductPrice = (product: any) => {
    if (product.selectedSize && product.menuListPriceList) {
        const priceInfo = product.menuListPriceList.find((price: any) => price.exceptionKey === product.selectedSize)
        if (priceInfo) {
            // 提取价格数字（去掉￥符号）
            return parseFloat(priceInfo.exceptionText.replace('￥', ''))
        }
    }
    // 默认返回第一个价格
    if (product.menuListPriceList && product.menuListPriceList.length > 0) {
        return parseFloat(product.menuListPriceList[0].exceptionText.replace('￥', ''))
    }
    return 0
}

// 获取商品的促销标签
const getProductTags = (product: any) => {
    const tags = []
    if (product.recommendType === 1) {
        tags.push('热门')
    }
    if (product.customerFields && product.customerFields.length > 0) {
        product.customerFields.forEach((field: any) => {
            if (field.exceptionText && field.exceptionText !== '下单人人有礼') {
                tags.push(field.exceptionText)
            }
        })
    }
    return tags
}

// 方法
const searchAddress = () => {
    if (!orderForm.address) {
        ElMessage.warning('请输入地址信息')
        return
    }
    ElMessage.success('地址搜索功能开发中...')
}

const clearRecognition = () => {
    recognitionText.value = ''
}

const recognizeInfo = async () => {
    if (!recognitionText.value.trim()) {
        ElMessage.warning('请输入收件信息')
        return
    }

    recognizing.value = true

    try {
        // 模拟识别过程
        await new Promise(resolve => setTimeout(resolve, 1500))

        // 简单的信息提取逻辑（实际项目中应该使用更复杂的算法）
        const text = recognitionText.value
        const phoneMatch = text.match(/1[3-9]\d{9}/)
        const nameMatch = text.match(/[\u4e00-\u9fa5]{2,4}/)

        if (phoneMatch) {
            orderForm.phone = phoneMatch[0]
        }
        if (nameMatch) {
            orderForm.recipient = nameMatch[0]
        }

        // 提取地址信息（简化版）
        const addressKeywords = ['市', '区', '街道', '路', '号']
        for (const keyword of addressKeywords) {
            const index = text.indexOf(keyword)
            if (index > -1) {
                const start = Math.max(0, index - 20)
                const end = Math.min(text.length, index + 30)
                orderForm.address = text.substring(start, end)
                break
            }
        }

        ElMessage.success('信息识别完成')
    } catch (error) {
        ElMessage.error('识别失败，请重试')
    } finally {
        recognizing.value = false
    }
}

const switchCategory = (category: string) => {
    activeCategory.value = category
    filterProducts()
}



const addToCart = (product: any) => {
    // 如果商品有多个规格，需要先选择规格
    if (product.menuListPriceList && product.menuListPriceList.length > 1) {
        // 显示规格选择对话框
        showSizeSelection(product)
        return
    }

    // 默认选择第一个规格
    const selectedSize = product.menuListPriceList && product.menuListPriceList.length > 0
        ? product.menuListPriceList[0].exceptionKey
        : null

    const cartKey = `${product.productCode}_${selectedSize}`
    const existingItem = cartItems.value.find(item => item.cartKey === cartKey)

    if (existingItem) {
        existingItem.quantity += 1
    } else {
        cartItems.value.push({
            ...product,
            cartKey,
            selectedSize,
            quantity: 1
        })
    }

    ElMessage.success(`${product.productName} 已加入购物车`)
}

// 商品详情弹窗相关
const detailDialogVisible = ref(false)
const currentDetailProduct = ref<any>(null)

const showSizeSelection = async (product: any) => {
    // 使用商品详情弹窗替代原来的消息框
    currentDetailProduct.value = product
    detailDialogVisible.value = true
}

const updateCartItem = (item: any) => {
    if (item.quantity <= 0) {
        removeFromCart(item.id)
    }
}

const removeFromCart = (cartKey: string) => {
    const index = cartItems.value.findIndex(item => item.cartKey === cartKey)
    if (index > -1) {
        cartItems.value.splice(index, 1)
        ElMessage.success('商品已从购物车移除')
    }
}

// 获取商品价格文本
const getProductPriceText = (product: any) => {
    if (product.selectedSize && product.menuListPriceList) {
        const priceInfo = product.menuListPriceList.find((price: any) => price.exceptionKey === product.selectedSize)
        if (priceInfo) {
            return priceInfo.exceptionText
        }
    }
    if (product.menuListPriceList && product.menuListPriceList.length > 0) {
        return product.menuListPriceList[0].exceptionText
    }
    return '价格待定'
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
    const img = event.target as HTMLImageElement
    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5IiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lm77niYfmlqDovb3lpLHotKU8L3RleHQ+PC9zdmc+'
}

const submitOrder = async () => {
    // 验证表单
    if (!orderFormRef.value) return

    try {
        await orderFormRef.value.validate()

        if (cartItems.value.length === 0) {
            ElMessage.warning('请先添加商品到购物车')
            return
        }

        await ElMessageBox.confirm(
            `确认提交订单吗？总金额：¥${totalPrice.value}`,
            '确认订单',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )

        // 模拟提交订单
        ElMessage.success('订单提交成功！')

        // 清空购物车
        cartItems.value = []
        showCart.value = false

    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('表单验证失败，请检查输入信息')
        }
    }
}

// 获取商品详情
const getProductDetail = async (productCode: string) => {
    try {
        const response = await apiGetProductDetail(productCode)
        return response.data.product
    } catch (error) {
        console.error('获取商品详情失败:', error)
        throw error
    }
}

// 处理从商品详情弹窗添加到购物车
const handleAddToCart = (cartItem: any) => {
    const existingItem = cartItems.value.find(item => item.cartKey === cartItem.cartKey)

    if (existingItem) {
        existingItem.quantity += cartItem.quantity
    } else {
        cartItems.value.push(cartItem)
    }

    ElMessage.success(`${cartItem.productName}${cartItem.selectedSize ? `(${cartItem.selectedSize})` : ''} 已加入购物车`)
}

// 加载所有商品数据（只调用一次）
const loadAllProducts = async () => {
    try {
        loading.value = true
        const response = await getProducts()
        // 处理商品数据
        const processedProducts = processProductList(response.data || [])
        // 保存所有商品数据
        allProducts.value = processedProducts
        // 初始显示所有商品
        filterProducts()
    } catch (error) {
        console.error('加载商品失败:', error)
        ElMessage.error('加载商品失败')
    } finally {
        loading.value = false
    }
}

// 处理商品列表数据
const processProductList = (productList: any[]) => {
    try {
        // 获取当前门店代码，如果没有选择门店则使用默认值
        const storeCode = orderForm.value?.store || 'default'

        if (!Array.isArray(productList)) {
            console.warn('商品列表数据格式错误:', productList)
            return []
        }

        const result = [...productList]

        result.forEach((info, index) => {
            if (!info) return

            try {
                // 处理价格列表 - 这是商品的核心价格信息
                if (info.menuListPriceList && Array.isArray(info.menuListPriceList)) {
                    // 根据当前条件筛选适用的价格项
                    result[index].menuListPriceList = filterPriceListByConditions(
                        info.menuListPriceList,
                        storeCode
                    )
                }

                // 处理其他异常字段（如果存在）
                if (info.nameExceptions && Array.isArray(info.nameExceptions)) {
                    const applicableException = findApplicableException(info.nameExceptions, storeCode)
                    if (applicableException && applicableException.value) {
                        result[index].productName = applicableException.value
                    }
                }

                if (info.descriptionExceptions && Array.isArray(info.descriptionExceptions)) {
                    const applicableException = findApplicableException(info.descriptionExceptions, storeCode)
                    if (applicableException && applicableException.value) {
                        result[index].description = applicableException.value
                    }
                }

                if (info.imageURL2Exceptions && Array.isArray(info.imageURL2Exceptions)) {
                    const applicableException = findApplicableException(info.imageURL2Exceptions, storeCode)
                    if (applicableException && applicableException.value) {
                        result[index].imageURL2 = applicableException.value
                    }
                }

            } catch (error) {
                console.warn(`处理商品 ${index} 的数据时出错:`, error)
            }
        })

        return result
    } catch (error) {
        console.error('处理商品列表数据时出错:', error)
        return productList || []
    }
}

// 根据条件筛选价格列表
const filterPriceListByConditions = (priceList: any[], storeCode: string) => {
    const currentDate = new Date()
    const currentDay = currentDate.getDay() // 0=周日, 1=周一, ...

    return priceList.filter(priceItem => {
        const rule = priceItem.effectiveRule
        if (!rule) return true // 没有规则则默认适用

        // 检查门店组
        if (rule.effectiveStoreGroups && Array.isArray(rule.effectiveStoreGroups)) {
            const storeGroup = getStoreGroup(storeCode)
            if (storeGroup && !rule.effectiveStoreGroups.includes(storeGroup)) {
                return false
            }
        }

        // 检查生效日期
        if (rule.effectiveDate && Array.isArray(rule.effectiveDate)) {
            const now = currentDate.getTime()
            const isInDateRange = rule.effectiveDate.some((dateRange: any) => {
                return now >= dateRange.effectiveStartDate && now <= dateRange.effectiveEndDate
            })
            if (!isInDateRange) return false
        }

        // 检查生效星期
        if (rule.effectiveDayofWeek && Array.isArray(rule.effectiveDayofWeek)) {
            if (!rule.effectiveDayofWeek.includes(currentDay)) {
                return false
            }
        }

        return true
    })
}

// 查找适用的异常配置
const findApplicableException = (exceptions: any[], storeCode: string) => {
    // reverse方法是为了让第一条匹配到的信息生效
    const reversedExceptions = [...exceptions].reverse()

    for (const exception of reversedExceptions) {
        const rule = exception.effectiveRule
        if (!rule) return exception // 没有规则则默认适用

        // 这里可以添加更复杂的规则匹配逻辑
        // 暂时简化处理，直接返回第一个匹配的
        return exception
    }

    return null
}

// 获取门店对应的门店组
const getStoreGroup = (storeCode: string): string | null => {
    const storeGroupMap: Record<string, string> = {
        'shanghai_jingan': 'G_ShangHai',
        'shanghai_xuhui': 'G_ShangHai',
        'shanghai_pudong': 'G_ShangHai',
        'beijing_chaoyang': 'G_BeiJing',
        'beijing_haidian': 'G_BeiJing',
        'guangzhou_tianhe': 'G_Guangzhou',
        'shenzhen_futian': 'G_ShenZhen'
    }
    return storeGroupMap[storeCode] || null
}

// 前端筛选商品
const filterProducts = () => {
    let filtered = [...allProducts.value]

    // 按分类筛选
    if (activeCategory.value !== 'all') {
        // 加载分类数据以获取分类映射
        const categoryMapping = buildCategoryMapping(activeCategory.value)

        if (categoryMapping.length > 0) {
            filtered = filtered.filter(product =>
                categoryMapping.includes(product.categoryCode)
            )
        } else {
            // 如果没有找到对应的分类代码，尝试使用 productSubType 筛选
            filtered = filtered.filter(product =>
                product.productSubType === activeCategory.value
            )
        }
    }

    // 按关键词筛选
    if (searchKeyword.value && searchKeyword.value.trim()) {
        const keyword = searchKeyword.value.trim().toLowerCase()
        filtered = filtered.filter(product =>
            product.productName.toLowerCase().includes(keyword) ||
            (product.description && product.description.toLowerCase().includes(keyword))
        )
    }

    products.value = filtered
}

// 构建分类映射（前端版本）
const buildCategoryMapping = (targetCategory: string): string[] => {
    const categoryCodes: string[] = []

    originalCategoriesData.value.forEach(category => {
        // 检查一级分类
        if (category.categoryNameEN && category.categoryNameEN.trim() === targetCategory) {
            categoryCodes.push(category.categoryCode)

            // 添加所有子分类
            if (category.childNode && Array.isArray(category.childNode)) {
                category.childNode.forEach((child: any) => {
                    categoryCodes.push(child.categoryCode)
                })
            }
        }

        // 检查子分类
        if (category.childNode && Array.isArray(category.childNode)) {
            category.childNode.forEach((child: any) => {
                if (child.categoryNameEN && child.categoryNameEN.trim() === targetCategory) {
                    categoryCodes.push(child.categoryCode)
                }
            })
        }
    })

    return categoryCodes
}

// 加载分类数据
const loadCategories = async () => {
    try {
        const response = await getCategories()
        if (response.data && Array.isArray(response.data)) {
            // 保存原始分类数据
            originalCategoriesData.value = response.data

            // 处理一级分类
            const mainCategories = [
                { label: '全部', value: 'all', categoryCode: 'all', level: 0 }
            ]

            response.data.forEach(category => {
                if (category.categoryName && category.categoryNameEN && category.categoryLevel === '1') {
                    mainCategories.push({
                        label: category.categoryName,
                        value: category.categoryNameEN.trim(),
                        categoryCode: category.categoryCode,
                        level: category.categoryLevel,
                        childNode: category.childNode || []
                    })
                }
            })

            categories.value = mainCategories
        }
    } catch (error) {
        console.error('加载分类失败:', error)
        ElMessage.error('加载分类失败')
        // 只保留"全部"选项
        categories.value = [
            { label: '全部', value: 'all', categoryCode: 'all', level: 0 }
        ]
    }
}

// 切换一级分类
const switchMainCategory = (category: any) => {
    selectedMainCategory.value = category

    if (category.value === 'all') {
        // 选择"全部"时，清空二级分类，直接显示所有商品
        subCategories.value = []
        activeCategory.value = 'all'
        filterProducts()
    } else {
        // 加载对应的二级分类
        loadSubCategories(category)
        // 默认选择该一级分类，显示该分类下的所有商品
        activeCategory.value = category.value

        filterProducts()
    }
}

// 加载二级分类
const loadSubCategories = (mainCategory: any) => {
    if (mainCategory.childNode && Array.isArray(mainCategory.childNode)) {
        const subs = mainCategory.childNode.map((child: any) => ({
            label: child.categoryName,
            value: child.categoryNameEN.trim(),
            categoryCode: child.categoryCode,
            level: child.categoryLevel,
            parentCode: mainCategory.categoryCode
        }))

        // 如果有二级分类，添加"全部"选项
        if (subs.length > 0) {
            subCategories.value = [
                {
                    label: `全部${mainCategory.label}`,
                    value: mainCategory.value,
                    categoryCode: mainCategory.categoryCode,
                    level: mainCategory.level,
                    isMainCategory: true
                },
                ...subs
            ]
        } else {
            subCategories.value = []
        }
    } else {
        subCategories.value = []
    }
}

// 监听门店变化，重新处理商品数据
watch(() => orderForm.store, (newStore, oldStore) => {
    if (newStore && newStore !== oldStore && allProducts.value.length > 0) {
        // 门店变化时重新处理商品数据
        const processedProducts = processProductList(allProducts.value)
        allProducts.value = processedProducts
        filterProducts()
        ElMessage.info(`已切换到${getStoreName(newStore)}，商品信息已更新`)
    }
})

// 获取门店名称
const getStoreName = (storeCode: string) => {
    const storeMap: Record<string, string> = {
        'shanghai_jingan': '上海静安店',
        'shanghai_xuhui': '上海徐汇店',
        'shanghai_pudong': '上海浦东店'
    }
    return storeMap[storeCode] || storeCode
}

// 生命周期
onMounted(async () => {
    ElMessage.info('欢迎使用达美乐代下单服务')
    await loadCategories() // 先加载分类
    await loadAllProducts() // 加载所有商品数据

    // 默认选择"全部"分类
    if (categories.value.length > 0) {
        switchMainCategory(categories.value[0]) // 选择"全部"
    }
})

</script>

<style scoped lang="scss">
// 页面整体样式
.order-create-page {
    padding: 20px;
    background: #f5f7fa;
    min-height: 100vh;
}

// 信息区域布局
.info-section {
    margin-bottom: 20px;
}

// 卡片通用样式
.order-info-card,
.recognition-card,
.menu-card {
    margin-bottom: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #e60012;

        .header-icon {
            font-size: 1.2rem;
        }
    }
}

// 地址警告样式
.address-warning {
    margin-top: 15px;
}

// 一键识别区域
.recognition-content {
    .recognition-tip {
        color: #666;
        margin-bottom: 15px;
        font-size: 14px;
        line-height: 1.5;
    }

    .recognition-textarea {
        margin-bottom: 15px;
    }

    .recognition-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
}

// 确保两个卡片高度一致
.order-info-card,
.recognition-card {
    height: 100%;

    :deep(.el-card__body) {
        height: calc(100% - 60px);
        display: flex;
        flex-direction: column;
    }
}

// 菜单商品区域
.menu-content {
    .menu-search {
        margin-bottom: 20px;

        .search-input {
            max-width: 400px;
        }
    }

    .category-tabs {
        margin-bottom: 15px;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;

        .category-btn {
            border-radius: 20px;
            padding: 8px 20px;
            font-weight: 500;
        }
    }

    .sub-category-tabs {
        margin-bottom: 25px;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        padding-left: 20px;
        border-left: 3px solid #e60012;
        background: rgba(230, 0, 18, 0.05);
        padding: 10px 15px;
        border-radius: 8px;

        .sub-category-btn {
            border-radius: 15px;
            padding: 4px 12px;
            font-weight: 400;
            font-size: 13px;
        }
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;

        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;

            &:hover {
                transform: translateY(-4px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }

            .product-image {

                overflow: hidden;
                background: #f8f9fa;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 15px;

                img {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                    border-radius: 8px;
                }
            }

            .product-info {
                padding: 20px;

                .product-price {
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: #e60012;
                    margin-bottom: 8px;
                }

                .product-name {
                    font-size: 1.1rem;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 10px;
                    line-height: 1.4;
                }

                .product-badges {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 6px;
                    margin-bottom: 10px;
                }

                .product-badge {
                    background: #e60012;
                    color: white;
                    padding: 3px 8px;
                    border-radius: 12px;
                    font-size: 11px;
                    font-weight: 600;

                    &.hot-badge {
                        background: #ff6b35;
                    }
                }

                .product-desc {
                    color: #666;
                    font-size: 14px;
                    line-height: 1.5;
                    margin-bottom: 12px;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }

                .size-price-list {
                    margin-bottom: 15px;

                    .size-options {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 8px;
                        align-items: center;
                    }

                    .size-option {
                        display: flex;
                        align-items: center;
                        gap: 4px;

                        .size-name {
                            font-size: 14px;
                            color: #333;
                            font-weight: 500;
                        }

                        .size-price {
                            font-size: 14px;
                            color: #e60012;
                            font-weight: 700;
                        }

                        &:not(:last-child)::after {
                            content: '|';
                            color: #ddd;
                            margin-left: 8px;
                        }
                    }
                }

                .add-to-cart-btn {
                    width: 100%;
                    border-radius: 8px;
                    font-weight: 600;
                    padding: 12px;
                }
            }
        }
    }
}

// 购物车悬浮按钮
.cart-float {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    cursor: pointer;

    .cart-badge {
        .el-button {
            width: 60px;
            height: 60px;
            box-shadow: 0 4px 12px rgba(230, 0, 18, 0.3);

            &:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 20px rgba(230, 0, 18, 0.4);
            }
        }
    }
}

// 购物车内容
.cart-content {
    .empty-cart {
        text-align: center;
        padding: 40px 20px;
    }

    .cart-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #eee;

        &:last-child {
            border-bottom: none;
        }

        .cart-item-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
            margin-right: 15px;
        }

        .cart-item-info {
            flex: 1;

            .cart-item-name {
                font-weight: 600;
                margin-bottom: 5px;
                color: #333;

                .cart-item-size {
                    color: #666;
                    font-weight: 400;
                    font-size: 12px;
                }
            }

            .cart-item-price {
                color: #e60012;
                font-weight: 600;
            }
        }

        .cart-item-actions {
            display: flex;
            align-items: center;
        }
    }

    .cart-total {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 2px solid #eee;

        .total-price {
            font-size: 1.2rem;
            font-weight: 700;
            color: #e60012;
            text-align: center;
            margin-bottom: 15px;
        }
    }
}

// 响应式设计
@media screen and (max-width: 768px) {
    .order-create-page {
        padding: 15px;
    }

    .info-section {
        .el-col {
            margin-bottom: 20px;
        }
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .category-tabs {
        justify-content: center;
    }

    .sub-category-tabs {
        padding-left: 10px;

        .sub-category-btn {
            width: calc(50% - 4px);
            margin-bottom: 5px;
        }
    }

    .cart-float {
        bottom: 20px;
        right: 20px;

        .cart-badge .el-button {
            width: 50px;
            height: 50px;
        }
    }

    // 移动端取消卡片高度一致
    .order-info-card,
    .recognition-card {
        height: auto;

        :deep(.el-card__body) {
            height: auto;
        }
    }
}

@media screen and (max-width: 480px) {
    .recognition-actions {
        flex-direction: column;

        .el-button {
            width: 100%;
        }
    }

    .menu-search .search-input {
        max-width: 100%;
    }
}

// 暗色主题支持
html.dark {
    .order-create-page {
        background: var(--el-bg-color-page);
    }

    .order-info-card,
    .recognition-card,
    .menu-card {
        background: var(--el-bg-color);

        .card-header {
            color: var(--el-color-danger);
        }
    }

    .products-grid .product-card {
        background: var(--el-bg-color);

        .product-info {
            .product-name {
                color: var(--el-text-color-primary);
            }

            .product-desc {
                color: var(--el-text-color-regular);
            }
        }
    }

    .cart-content {
        .cart-item {
            border-bottom-color: var(--el-border-color);

            .cart-item-info .cart-item-name {
                color: var(--el-text-color-primary);
            }
        }

        .cart-total {
            border-top-color: var(--el-border-color);
        }
    }
}
</style>
