<template>
  <el-dialog
    v-model="dialogVisible"
    :title="product?.productName || '商品详情'"
    width="80%"
    max-width="800px"
    destroy-on-close
    class="product-detail-dialog"
  >
    <div class="product-detail-content">
      <!-- 顶部：商品基本信息区 -->
      <div class="product-top-section">
        <div class="product-main-info">
          <!-- 商品图片 -->
          <div class="product-image">
            <img
              :src="product?.imageURL1 || '/placeholder.svg?height=300&width=300'"
              :alt="product?.productName"
              class="main-image"
              @error="handleImageError"
            />
          </div>

          <!-- 商品信息 -->
          <div class="product-info">
            <h2 class="product-title">{{ product?.productName }}</h2>

            <!-- 商品标签 -->
            <div class="product-badges">
              <div
                v-for="tag in getProductTags(product)"
                :key="tag"
                class="product-badge"
                :class="{ 'hot-badge': tag === '热门' }"
              >
                {{ tag }}
              </div>
            </div>

            <!-- 披萨介绍 -->
            <div class="pizza-intro">
              <div class="product-description">{{ product?.description || productResource?.description }}</div>
              <div class="base-ingredients" v-if="productResource?.description2">
                <span class="ingredients-text">{{ productResource.description2 }}</span>
              </div>
            </div>

            <!-- 价格显示 -->
            <div class="product-price">
              <span class="current-price">{{ getTotalPriceText() }}</span>
            </div>



            <!-- 规格选择区域 -->
            <div class="specs-selection">
              <!-- 尺寸和淋酱并排选择 -->
              <div class="size-sauce-row">
                <!-- 尺寸选择 -->
                <div v-if="product?.menuListPriceList && product?.menuListPriceList.length > 0" class="size-selection">
                  <h4 class="spec-title">尺寸</h4>
                  <div class="size-options">
                    <div
                      v-for="priceItem in product.menuListPriceList"
                      :key="priceItem.exceptionKey"
                      class="size-option"
                      :class="{ 'selected': selectSizeCode === priceItem.exceptionKey }"
                      @click="selectSize(priceItem.exceptionKey)"
                    >
                      <!-- 尺寸图片 -->
                      <div class="size-image">
                        <img
                          :src="getSizeImage(priceItem.exceptionKey)"
                          :alt="priceItem.exceptionKey"
                          class="size-img"
                          @error="handleImageError"
                        />
                      </div>
                      <div class="size-info">
                        <div class="size-title" v-if="getSizeCode(priceItem.exceptionKey) == '7'">7"(英寸)</div>
                        <div class="size-title" v-if="getSizeCode(priceItem.exceptionKey) == 'S'">9"(英寸)</div>
                        <div class="size-title" v-if="getSizeCode(priceItem.exceptionKey) == 'L'">12"(英寸)</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 淋酱选择 -->
                <div class="sauce-selection" v-if="sauceList.length > 0">
                  <h4 class="spec-title">底酱+淋酱</h4>
                  <div class="sauce-options">
                    <div
                      v-for="(sauce, index) in sauceList.slice(0, 3)"
                      :key="`sauce-${index}`"
                      class="sauce-option"
                      :class="{ 'selected': sauce.isDefault === 1 }"
                      @click="changeSauce(sauce)"
                    >
                      <div class="sauce-icon">
                        <img :src="sauce.saucesImage || sauce.imageURL1 || '/placeholder.svg'" :alt="sauce.name" class="sauce-image" />
                      </div>
                      <div class="sauce-name">{{ sauce.name }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 数量选择和加入购物车 -->
            <div class="quantity-cart-section">
              <div class="quantity-selection">
                <h4 class="spec-title">数量</h4>
                <div class="custom-quantity-input">
                  <button @click="decreaseQuantity" :disabled="quantity <= 1" class="qty-circle-btn">-</button>
                  <span class="quantity-display">{{ quantity }}</span>
                  <button @click="increaseQuantity" class="qty-circle-btn">+</button>
                </div>
              </div>

              <div class="add-to-cart-section">
                <el-button type="primary" size="large" @click="addToCart" class="add-cart-btn">
                  加入购物车
                </el-button>
              </div>
            </div>
            </div>
          </div>
        </div>
      </div>



      <!-- 中间：扩展选择层 -->
      <div class="product-middle-section">
        <!-- 饼底扩展选择 -->
        <div class="crust-section" v-if="pizzaVariants.length > 0">
          <h3 class="section-title">饼底</h3>
          <div class="crust-grid">
            <div
              v-for="variant in pizzaVariants"
              :key="variant.code"
              class="crust-item"
              :class="{ 'selected': selectPastryInfo?.code === variant.code }"
              @click="selectPastry(variant)"
            >
              <img :src="variant.image" :alt="variant.name" class="crust-image" />
              <div class="crust-name">{{ variant.name }}</div>
            </div>
          </div>
        </div>

        <!-- 配料区 -->
        <div class="ingredients-section">
          <!-- 基础配料 -->
          <div class="base-ingredients" v-if="baseIngredientsList.length > 0">
            <h3 class="section-title">基础配料</h3>
            <div class="ingredients-grid">
              <div
                v-for="(ingredient, index) in baseIngredientsList"
                :key="`base-${index}`"
                class="ingredient-card base-ingredient"
              >
                <img :src="ingredient.imageURL1 || '/placeholder.svg'" :alt="ingredient.name" class="ingredient-image" />
                <div class="ingredient-info">
                  <div class="ingredient-name">{{ ingredient.name }}</div>
                  <div class="ingredient-price" v-if="ingredient.productPrice > 0">
                    +￥{{ ingredient.productPrice }}
                  </div>
                  <div class="ingredient-controls">
                    <button @click="decreaseBaseIngredient(ingredient)" :disabled="ingredient.buyNum <= (ingredient.minSelectable || 0)" class="qty-circle-btn">-</button>
                    <span class="ingredient-quantity">{{ ingredient.buyNum || 0 }}</span>
                    <button @click="increaseBaseIngredient(ingredient)" :disabled="ingredient.buyNum >= (ingredient.maxSelectable || 99)" class="qty-circle-btn">+</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 添加配料 -->
          <div class="additional-ingredients" v-if="additionalIngredientsList.length > 0">
            <h3 class="section-title">添加配料</h3>
            <div class="ingredients-grid">
              <div
                v-for="(ingredient, index) in additionalIngredientsList"
                :key="`additional-${index}`"
                class="ingredient-card additional-ingredient"
              >
                <img :src="ingredient.imageURL1 || '/placeholder.svg'" :alt="ingredient.name" class="ingredient-image" />
                <div class="ingredient-info">
                  <div class="ingredient-name">{{ ingredient.name }}</div>
                  <div class="ingredient-price" v-if="ingredient.productPrice > 0">
                    +￥{{ ingredient.productPrice }}
                  </div>
                  <div class="ingredient-controls">
                    <button @click="decreaseIngredient(ingredient)" :disabled="ingredient.buyNum <= (ingredient.minSelectable || 0)" class="qty-circle-btn">-</button>
                    <span class="ingredient-quantity">{{ ingredient.buyNum || 0 }}</span>
                    <button @click="increaseIngredient(ingredient)" :disabled="ingredient.buyNum >= (ingredient.maxSelectable || 99)" class="qty-circle-btn">+</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部：商品详情图片 -->
      <div class="product-bottom-section" v-if="detailImages.length > 0">
        <h3 class="section-title">商品详情</h3>
        <div class="detail-images">
          <img
            v-for="(image, index) in detailImages"
            :key="index"
            :src="image"
            :alt="`商品详情图片${index + 1}`"
            class="detail-image"
          />
        </div>
      </div>





  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  product: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'add-to-cart'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 选择的规格
const selectedSize = ref('')
// 选择的饼底
const selectedCrust = ref(0)
// 商品数量
const quantity = ref(1)

// 饼底选项
const crustOptions = [
  {
    name: '芝心酱料比山',
    desc: '香浓起司',
    image: '/placeholder.svg?height=80&width=80'
  },
  {
    name: '黄金芝心',
    desc: '芝士王',
    image: '/placeholder.svg?height=80&width=80'
  }
]

import { getProductDetail as apiGetProductDetail } from '/@/api/mce/product'

// 披萨变体展示
const pizzaVariants = ref([])

// 配料展示
const ingredients = ref([])

// 商品详情相关数据
const productResource = ref<any>(null)
const productInfo = ref<any>({})
const selectSizeCode = ref('')
const selectPastryInfo = ref<any>(null)
const selectSauceInfo = ref<any>(null)
const drenchSauceList = ref<any[]>([])
const drenchSauceMap = ref<any>({})
const pizzaPrice = ref(0)
const productNum = ref(1)
const bigPastryImageInfo = ref<any[]>([])

// 添加价格组件变量 - 基于 dd.js
const sauceCountPrice = ref(0)
const additionalIngrCountPrice = ref(0)
const baseIngrCountPrice = ref(0)
const ingCombosCountPrice = ref(0)

// 数据映射
const pizzaPastryMap = ref<any>({})
const pastryMap = ref<any>({})
const sizeMap = ref<any>({})
const sauceList = ref<any[]>([])
const baseIngredientsList = ref<any[]>([])
const additionalIngredientsList = ref<any[]>([])
const descriptionInfo = ref<any>({})

// 加载商品详情数据 - 根据实际数据格式实现
const loadProductDetail = async (productCode) => {
  if (!productCode) return

  try {
    // 初始化
    drenchSauceList.value = []
    drenchSauceMap.value = {}

    const res = await apiGetProductDetail(productCode)
    if (!res.data || !res.data.product) return

    // 存储商品源详情 - 后端返回 { product: productDetail }
    productResource.value = res.data.product

    // 1. 披萨描述内容分解
    const description = res.data.product.description || ''
    descriptionInfo.value = description.split('[br]').reduce((map, str) => {
      if (str.match(/^配料/)) {
        map['ingredient'] = str
      } else if (str.match(/^规格/)) {
        map['spec'] = str
      } else {
        map['description'] = str
      }
      return map
    }, {})

    // 2. 比萨code map (用于对下面饼底map合并使用)
    pizzaPastryMap.value = (res.data.product.pastryList || []).reduce((map, info, currindex) => {
      info.index = currindex
      map[info.code] = info
      return map
    }, {})

    // 3. 饼底map (按尺寸分组)
    pastryMap.value = (res.data.product.productList || []).reduce((map, info) => {
      (map[info.sizeCode] = map[info.sizeCode] || []).push(
        Object.assign({}, info, pizzaPastryMap.value[info.pastryCode])
      )
      map[info.sizeCode] = map[info.sizeCode].sort((a, b) => a.index - b.index)
      return map
    }, {})

    // 4. 尺寸字典
    sizeMap.value = (res.data.product.sizeList || []).reduce((map, info) => {
      map[info.code] = info
      return map
    }, {})

    // 继续处理其他数据...
    processProductData(res.data.product)

    console.log('商品详情数据加载完成:', {
      productResource: productResource.value,
      pastryList: res.data.product.pastryList,
      baseIngredientList: res.data.product.baseIngredientList,
      additionalIngredientList: res.data.product.additionalIngredientList
    })

  } catch (error) {
    console.error('获取商品详情失败:', error)
    ElMessage.error('获取商品详情失败')
  }
}

// 处理商品数据的核心逻辑
const processProductData = (res) => {
  // 5. 酱料选择数量处理
  sauceList.value = (res.sauceList || []).map((info) => {
    info.productPrice = 0
    if (info.isDefault) {
      info.isDefault = 1
    } else {
      info.isDefault = 0
    }
    info.saucesImage = info.imageURL2 || info.imageURL1
    return info
  })

  const selectedSauces = sauceList.value.filter(item => item.isDefault == 1)
  const sauceSelectNumber = selectedSauces.length || 0

  // 6. 饼底栏下边轮播图片
  bigPastryImageInfo.value = res.bigPastryImageInfo || []

  // 7. 处理商品信息
  productInfo.value = {
    sauceSelectNumber,
    descriptionInfo: descriptionInfo.value,
    productName: res.productName,
    productNameEN: res.productNameEN,
    description: res.description,
    description2: res.description2,
    descriptionEN: res.descriptionEN || res.description,
    descriptionEN2: res.descriptionEN2 || res.description2,
    productImg: res.detailPageProImgUrl || res.imageURL2,
    sellingPrice: res.sellingPrice,
    isShowCouponPrice: res.isShowCouponPrice,
    productCode: res.productCode,
    detailImageURLs: res.detailImageURLs && res.detailImageURLs.split(';'),
    pastryMap: pastryMap.value,
    productList: res.productList
  }

  // 8. 处理尺寸列表 - 直接使用 sizeList
  productInfo.value.sizeList = (res.sizeList || []).sort((a, b) => a.sort - b.sort)

  // 9. 处理酱料
  productInfo.value.sauceList = sauceList.value

  // 10. 处理饼底
  productInfo.value.pastryList = res.pastryList || []

  // 继续处理配料和其他数据
  processIngredientsData(res)
}

// 处理配料数据
const processIngredientsData = (res) => {
  // 11. 处理做法 - 如果有 ingCombos
  productInfo.value.ingCombos = (res.ingCombos || []).map((item) => {
    item.ingList = (item.ingList || []).map(function(info) {
      info.imageUrl = info.imageURL2 || info.imageURL1
      info.isDefault = info.isDefault ? 1 : 0
      info.buyNum = info.isDefault ? info.initQuantity || 1 : 0
      info.freeQuantity = info.freeQuantity || 0
      info.isAdditional = info.baseFlag === 0
      return info
    })
    return item
  })

  // 12. 配料处理 - 使用实际的字段名
  baseIngredientsList.value = (res.baseIngredientList || []).map((info) => {
    info.isDefault = 1
    info.buyNum = 1 // 基础配料默认1份
    // 如果没有价格信息，设置为0（表示基础包含）
    if (!info.productPrice) {
      info.productPrice = 0
    }
    return info
  })

  additionalIngredientsList.value = (res.additionalIngredientList || []).map((info) => {
    info.isDefault = 0
    info.buyNum = 0
    return info
  })

  productInfo.value.baseIngredientsList = baseIngredientsList.value
  productInfo.value.additionalIngredientsList = additionalIngredientsList.value

  // 13. 处理淋酱
  baseIngredientsList.value.concat(additionalIngredientsList.value).forEach((e) => {
    if (e.ingredientType === 1 && e.buyNum > 0 && !drenchSauceMap.value[e.code]) {
      drenchSauceMap.value[e.code] = true
      drenchSauceList.value.push(e)
    }
  })

  // 14. 设置默认选项
  setDefaultSelections(res)

  // 15. 更新显示数据
  updateDisplayData()
}

// 设置默认选项
const setDefaultSelections = (res) => {
  // 已选尺寸code
  let selectSizeCodeValue = ''
  let selectPastryCode = ''

  // 查找默认尺寸和饼底
  if (res.sizeList && res.productList) {
    res.sizeList.forEach((item) => {
      res.productList.forEach((val) => {
        if (val.isDefault && val.sizeCode == item.code) {
          selectSizeCodeValue = item.code
          selectPastryCode = val.pastryCode
        }
      })
    })
  }

  selectSizeCode.value = selectSizeCodeValue

  // 已选的饼底
  if (pastryMap.value[selectSizeCodeValue]) {
    selectPastryInfo.value = pastryMap.value[selectSizeCodeValue].find((info) => info.code == selectPastryCode) || pastryMap.value[selectSizeCodeValue][0]
  }

  // 已选的底酱
  if (res.sauceList) {
    selectSauceInfo.value = res.sauceList.find((item) => item.isDefault == true) || res.sauceList[0]
  }

  // 计算价格
  calculatePrice()
}

// 更新显示数据
const updateDisplayData = () => {
  // 更新饼底显示数据
  if (productInfo.value.pastryList && productInfo.value.pastryList.length > 0) {
    pizzaVariants.value = productInfo.value.pastryList.map(pastry => ({
      name: pastry.name,
      image: pastry.imageURL1 || '/placeholder.svg?height=100&width=100',
      code: pastry.code
    }))
  } else {
    pizzaVariants.value = []
  }

  // 更新配料显示数据
  const allIngredients = []

  // 添加基础配料
  if (baseIngredientsList.value && baseIngredientsList.value.length > 0) {
    baseIngredientsList.value.forEach(ingredient => {
      allIngredients.push({
        name: ingredient.name,
        image: ingredient.imageURL1 || '/placeholder.svg?height=80&width=80',
        code: ingredient.code,
        ingredientType: ingredient.ingredientType,
        buyNum: ingredient.buyNum,
        isBase: true
      })
    })
  }

  // 添加附加配料
  if (additionalIngredientsList.value && additionalIngredientsList.value.length > 0) {
    additionalIngredientsList.value.forEach(ingredient => {
      allIngredients.push({
        name: ingredient.name,
        image: ingredient.imageURL1 || '/placeholder.svg?height=80&width=80',
        code: ingredient.code,
        ingredientType: ingredient.ingredientType,
        buyNum: ingredient.buyNum,
        isBase: false
      })
    })
  }

  ingredients.value = allIngredients

  console.log('更新显示数据:', {
    pizzaVariants: pizzaVariants.value,
    ingredients: ingredients.value,
    baseIngredients: baseIngredientsList.value,
    additionalIngredients: additionalIngredientsList.value
  })
}

// 重新定义计算价格函数 - 基于 dd.js 的完整逻辑
const calculatePrice = () => {
  if (!productResource.value || !selectSizeCode.value) {
    pizzaPrice.value = 0
    return
  }

  // 1. 基础价格计算 - 饼底价格
  let basePrice = 0
  if (selectPastryInfo.value && selectPastryInfo.value.sellingPrice) {
    basePrice = parseFloat(selectPastryInfo.value.sellingPrice) || 0
  } else if (props.product?.menuListPriceList && selectSizeCode.value) {
    // 如果没有饼底价格，使用商品价格列表
    const priceInfo = props.product.menuListPriceList.find(price => price.exceptionKey === selectSizeCode.value)
    if (priceInfo && priceInfo.exceptionText) {
      basePrice = parseFloat(priceInfo.exceptionText.replace('￥', '')) || 0
    }
  }

  // 2. 酱料价格计算
  let saucePrice = 0
  if (sauceList.value && sauceList.value.length > 0) {
    saucePrice = sauceList.value.reduce((total, sauce) => {
      if (sauce.isDefault === 1) {
        const unitPrice = getIngredientPriceBySize(sauce, selectSizeCode.value)
        total += unitPrice
      }
      return total
    }, 0)
  }
  sauceCountPrice.value = saucePrice

  // 3. 配料组合价格计算 (ingCombos)
  let ingCombosPrice = 0
  if (productInfo.value.ingCombos && productInfo.value.ingCombos.length > 0) {
    const allIngCombos = productInfo.value.ingCombos.reduce((acc, combo) => {
      return acc.concat(combo.ingList || [])
    }, [])

    ingCombosPrice = allIngCombos.reduce((total, ingredient) => {
      if (ingredient.buyNum > 0) {
        const unitPrice = getIngredientPriceBySize(ingredient, selectSizeCode.value)
        const freeQuantity = ingredient.freeQuantity || 0
        const chargeableQuantity = ingredient.buyNum > freeQuantity ? ingredient.buyNum - freeQuantity : 0
        total += unitPrice * chargeableQuantity
        // 设置每个配料的选择价格，用于显示
        ingredient.selectPrice = unitPrice * chargeableQuantity
        ingredient.originalPrice = unitPrice
      }
      return total
    }, 0)
  }
  ingCombosCountPrice.value = ingCombosPrice

  // 4. 基础配料价格计算（超过1份的部分收费）
  let baseIngrPrice = 0
  if (baseIngredientsList.value) {
    baseIngrPrice = baseIngredientsList.value.reduce((total, ingredient) => {
      const unitPrice = getIngredientPriceBySize(ingredient, selectSizeCode.value)
      if (ingredient.buyNum > 1 && unitPrice > 0) {
        // 基础配料超过1份的部分需要额外收费
        const extraQuantity = ingredient.buyNum - 1
        total += unitPrice * extraQuantity
      }
      // 设置每个配料的选择价格，用于显示
      ingredient.selectPrice = ingredient.buyNum > 1 ? (ingredient.buyNum - 1) * unitPrice : 0
      ingredient.originalPrice = unitPrice
      return total
    }, 0)
  }
  baseIngrCountPrice.value = baseIngrPrice

  // 5. 附加配料价格计算
  let additionalIngrPrice = 0
  if (additionalIngredientsList.value) {
    additionalIngrPrice = additionalIngredientsList.value.reduce((total, ingredient) => {
      const unitPrice = getIngredientPriceBySize(ingredient, selectSizeCode.value)
      if (ingredient.buyNum > 0) {
        total += unitPrice * ingredient.buyNum
      }
      // 设置每个配料的选择价格，用于显示
      ingredient.selectPrice = ingredient.buyNum > 0 ? ingredient.buyNum * unitPrice : 0
      ingredient.originalPrice = unitPrice
      return total
    }, 0)
  }
  additionalIngrCountPrice.value = additionalIngrPrice

  // 6. 计算总价
  const totalPrice = basePrice + saucePrice + baseIngrPrice + additionalIngrPrice + ingCombosPrice
  pizzaPrice.value = parseFloat((totalPrice * productNum.value).toFixed(2))

  console.log('价格计算详情 (基于dd.js逻辑):', {
    basePrice: basePrice.toFixed(2),
    saucePrice: saucePrice.toFixed(2),
    baseIngrPrice: baseIngrPrice.toFixed(2),
    additionalIngrPrice: additionalIngrPrice.toFixed(2),
    ingCombosPrice: ingCombosPrice.toFixed(2),
    totalUnitPrice: totalPrice.toFixed(2),
    productNum: productNum.value,
    finalPrice: pizzaPrice.value,
    selectSizeCode: selectSizeCode.value
  })
}

// 酱料选择交互 - 基于 dd.js 的逻辑
const changeSauce = (sauceInfo) => {
  if (!productInfo.value.sauceList) return

  const { sauceSelectNumber, sauceList: sauceListData } = productInfo.value

  console.log('酱料选择:', sauceSelectNumber, sauceListData.length)

  // 如果酱料选择数量等于总数量，则不可取消（全部必选）
  if (sauceSelectNumber === sauceListData.length) {
    // 无需处理，全部必选
    return
  }

  if (sauceSelectNumber === 1) {
    // 只能选择1种酱料，选中当前的，取消其他的
    sauceListData.forEach((sauce) => {
      if (sauce.code === sauceInfo.code) {
        sauce.isDefault = 1
      } else {
        sauce.isDefault = 0
      }
    })
    selectSauceInfo.value = sauceInfo
  } else {
    // 可以选择多种酱料
    if (sauceInfo.isDefault === 1) {
      // 当前已选中，取消选中
      sauceInfo.isDefault = 0
    } else {
      // 当前未选中，检查是否超过限制
      const selectedCount = sauceListData.filter((sauce) => sauce.isDefault === 1).length
      if (selectedCount >= sauceSelectNumber) {
        ElMessage.warning(`最多选择${sauceSelectNumber}种，请取消后选择`)
        return
      } else {
        sauceInfo.isDefault = 1
      }
    }
  }

  calculatePrice()
}

// 选择饼底
const selectPastry = (pastryInfo) => {
  selectPastryInfo.value = pastryInfo
  calculatePrice()
}

// 配料数量变化处理 - 基于 dd.js 的 stepperVal 逻辑
const updateDrenchSauceList = () => {
  // 重置淋酱列表
  drenchSauceList.value = []
  drenchSauceMap.value = {}

  // 处理基础配料中的淋酱
  if (baseIngredientsList.value) {
    baseIngredientsList.value.forEach(ingredient => {
      if (ingredient.buyNum > 0 && ingredient.ingredientType === 1) {
        if (!drenchSauceMap.value[ingredient.code]) {
          drenchSauceMap.value[ingredient.code] = true
          drenchSauceList.value.push(ingredient)
        }
      } else if (ingredient.buyNum === 0 && ingredient.ingredientType === 1) {
        if (drenchSauceMap.value[ingredient.code]) {
          const index = drenchSauceList.value.findIndex(item => item.code === ingredient.code)
          if (index > -1) {
            drenchSauceList.value.splice(index, 1)
          }
          delete drenchSauceMap.value[ingredient.code]
        }
      }
    })
  }

  // 处理附加配料中的淋酱
  if (additionalIngredientsList.value) {
    additionalIngredientsList.value.forEach(ingredient => {
      if (ingredient.buyNum > 0 && ingredient.ingredientType === 1) {
        if (!drenchSauceMap.value[ingredient.code]) {
          drenchSauceMap.value[ingredient.code] = true
          drenchSauceList.value.push(ingredient)
        }
      } else if (ingredient.buyNum === 0 && ingredient.ingredientType === 1) {
        if (drenchSauceMap.value[ingredient.code]) {
          const index = drenchSauceList.value.findIndex(item => item.code === ingredient.code)
          if (index > -1) {
            drenchSauceList.value.splice(index, 1)
          }
          delete drenchSauceMap.value[ingredient.code]
        }
      }
    })
  }
}

// 增加基础配料数量
const increaseBaseIngredient = (ingredient) => {
  if (!ingredient.buyNum) {
    ingredient.buyNum = 0
  }
  const maxSelectable = ingredient.maxSelectable || 99
  if (ingredient.buyNum < maxSelectable) {
    ingredient.buyNum++
    ingredient.isDefault = ingredient.buyNum > 0 ? 1 : 0
    updateDrenchSauceList()
    calculatePrice()
  }
}

// 减少基础配料数量
const decreaseBaseIngredient = (ingredient) => {
  const minSelectable = ingredient.minSelectable || 0
  if (ingredient.buyNum > minSelectable) {
    ingredient.buyNum--
    ingredient.isDefault = ingredient.buyNum > 0 ? 1 : 0
    updateDrenchSauceList()
    calculatePrice()
  }
}

// 增加附加配料数量
const increaseIngredient = (ingredient) => {
  if (!ingredient.buyNum) {
    ingredient.buyNum = 0
  }
  const maxSelectable = ingredient.maxSelectable || 99
  if (ingredient.buyNum < maxSelectable) {
    ingredient.buyNum++
    ingredient.isDefault = ingredient.buyNum > 0 ? 1 : 0
    updateDrenchSauceList()
    calculatePrice()
  }
}

// 减少附加配料数量
const decreaseIngredient = (ingredient) => {
  const minSelectable = ingredient.minSelectable || 0
  if (ingredient.buyNum > minSelectable) {
    ingredient.buyNum--
    ingredient.isDefault = ingredient.buyNum > 0 ? 1 : 0
    updateDrenchSauceList()
    calculatePrice()
  }
}

// 是否显示饼底选项
const showCrustOptions = computed(() => {
  return props.product?.productSubType === 'Pizza'
})

// 详情图片列表
const detailImages = computed(() => {
  if (!productResource.value?.detailImageURLs) {
    return []
  }
  return productResource.value.detailImageURLs.split(';').filter(url => url.trim())
})

// 监听商品变化，重置选择并加载详情
watch(() => props.product, (newProduct) => {
  if (newProduct) {
    // 默认选择第一个规格
    if (newProduct.menuListPriceList && newProduct.menuListPriceList.length > 0) {
      selectedSize.value = newProduct.menuListPriceList[0].exceptionKey
    } else {
      selectedSize.value = ''
    }
    selectedCrust.value = 0
    quantity.value = 1
    productNum.value = 1

    // 加载商品详情数据
    if (newProduct.productCode) {
      loadProductDetail(newProduct.productCode)
    }
  }
}, { immediate: true })

// 获取商品的促销标签
const getProductTags = (product: any) => {
  const tags = []
  if (product?.recommendType === 1) {
    tags.push('热门')
  }
  if (product?.customerFields && product.customerFields.length > 0) {
    product.customerFields.forEach((field: any) => {
      if (field.exceptionText && field.exceptionText !== '下单人人有礼') {
        tags.push(field.exceptionText)
      }
    })
  }
  return tags
}

// 选择规格
const selectSize = (size: string) => {
  selectedSize.value = size
  selectSizeCode.value = size

  // 更新对应的饼底选项
  if (pastryMap.value[size] && pastryMap.value[size].length > 0) {
    selectPastryInfo.value = pastryMap.value[size][0]
  }

  calculatePrice()
}

// 获取选中规格的价格文本
const getSelectedPriceText = () => {
  if (!props.product?.menuListPriceList) return '价格待定'

  const priceInfo = props.product.menuListPriceList.find(
    (price: any) => price.exceptionKey === selectedSize.value
  )

  return priceInfo ? priceInfo.exceptionText : props.product.menuListPriceList[0].exceptionText
}

// 获取总价文本
const getTotalPriceText = () => {
  // 直接使用计算好的价格，因为 pizzaPrice.value 已经包含了数量
  if (pizzaPrice.value > 0) {
    return `¥${pizzaPrice.value.toFixed(2)}`
  }

  // 如果还没有计算过价格，使用基础价格
  if (!props.product?.menuListPriceList) return '¥0.00'

  const priceInfo = props.product.menuListPriceList.find(
    (price: any) => price.exceptionKey === selectedSize.value
  )

  if (priceInfo) {
    const priceValue = parseFloat(priceInfo.exceptionText.replace('￥', ''))
    return `¥${(priceValue * quantity.value).toFixed(2)}`
  }

  const defaultPrice = parseFloat(props.product.menuListPriceList[0].exceptionText.replace('￥', ''))
  return `¥${(defaultPrice * quantity.value).toFixed(2)}`
}

// 配料总数计算 - 基于 dd.js 的 ingrCount
const ingrCount = computed(() => {
  if (!selectSizeCode.value) return 0

  const baseCount = baseIngredientsList.value?.reduce((total, ingredient) => {
    return total + (ingredient.buyNum || 0)
  }, 0) || 0

  const additionalCount = additionalIngredientsList.value?.reduce((total, ingredient) => {
    return total + (ingredient.buyNum || 0)
  }, 0) || 0

  return baseCount + additionalCount
})

// 获取尺寸图片
const getSizeImage = (sizeName: string) => {
  if (!productResource.value?.sizeList) return '/placeholder.svg'

  // 根据尺寸名称找到对应的尺寸信息
  const sizeInfo = productResource.value.sizeList.find((size: any) => {
    return size.name === sizeName || size.name.includes(sizeName.replace('英寸', ''))
  })

  return sizeInfo?.imageURL1 || sizeInfo?.imageURL2 || '/placeholder.svg'
}

// 获取尺寸对应的 code
const getSizeCode = (sizeName: string) => {
  if (!productResource.value?.sizeList) return ''

  // 根据尺寸名称找到对应的尺寸信息
  const sizeInfo = productResource.value.sizeList.find((size: any) => {
    return size.name === sizeName || size.name.includes(sizeName.replace('英寸', ''))
  })

  return sizeInfo?.code || ''
}



// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5IiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lm77niYfmlqDovb3lpLHotKU8L3RleHQ+PC9zdmc+'
}

// 数量加减方法
const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
    productNum.value = quantity.value
    calculatePrice()
  }
}

const increaseQuantity = () => {
  if (quantity.value < 99) {
    quantity.value++
    productNum.value = quantity.value
    calculatePrice()
  }
}

// 添加到购物车 - 基于 dd.js 的 buyPizza 逻辑
const addToCart = () => {
  // 验证酱料选择 - 基于 dd.js 的验证逻辑
  if (productInfo.value.sauceList && productInfo.value.sauceSelectNumber) {
    const selectedSauces = productInfo.value.sauceList.filter(sauce => sauce.isDefault === 1)
    if (selectedSauces.length < productInfo.value.sauceSelectNumber) {
      ElMessage.warning(`至少选择${productInfo.value.sauceSelectNumber}种底酱`)
      return
    }
  }

  if (!selectedSize.value && props.product?.menuListPriceList && props.product.menuListPriceList.length > 0) {
    selectedSize.value = props.product.menuListPriceList[0].exceptionKey
  }

  // 构建完整的购物车对象，类似原始代码的 pizzObj
  const pizzObj = JSON.parse(
    JSON.stringify(
      Object.assign(
        // 复制非对象属性
        Object.keys(productResource.value || {}).reduce((map, key) => {
          if (typeof productResource.value[key] !== 'object') {
            map[key] = productResource.value[key]
          }
          return map
        }, {}),
        {
          // 尺寸
          sizeCode: selectSizeCode.value,
          // 饼皮 (选择的饼皮所有信息)
          pastryInfo: selectPastryInfo.value,
          // 酱料
          selectSauceInfo: selectSauceInfo.value,
          // 基本配料（包含用户调整的数量）
          baseIngredientsList: baseIngredientsList.value.map(ingredient => ({
            ...ingredient,
            buyNum: ingredient.buyNum || 1,
            isDefault: ingredient.buyNum > 0 ? 1 : 0
          })),
          // 附加配料（包含用户选择的数量）
          additionalIngredientsList: additionalIngredientsList.value.map(ingredient => ({
            ...ingredient,
            buyNum: ingredient.buyNum || 0,
            isDefault: ingredient.buyNum > 0 ? 1 : 0
          })),
          // 商品主图
          productImg: productInfo.value.productImg,
          // 商品总价
          countPrice: pizzaPrice.value,
          unitCustomPrice: pizzaPrice.value,
          // 购买数量
          productNum: productNum.value,
          // 酱料
          sauceList: productInfo.value.sauceList,
          ingCombos: productInfo.value.ingCombos || [],
          // 商品itemCode
          productCode: props.product?.productCode,
          productGroupCode: props.product?.productCode,
          flavorList: productResource.value?.flavorList,
          pizzaSizeName: productResource.value?.sizeList?.filter((item) => {
            return item.code == selectSizeCode.value
          }),
          // 购物车相关
          cartKey: `${props.product?.productCode}_${selectSizeCode.value}_${selectPastryInfo.value?.code}`,
          selectedSize: selectedSize.value,
          selectedCrust: selectedCrust.value,
          quantity: quantity.value
        }
      )
    )
  )

  emit('add-to-cart', pizzObj)
  dialogVisible.value = false
  ElMessage.success(`${props.product?.productName} 已加入购物车`)
}
</script>

<style scoped lang="scss">
.product-detail-dialog {
  :deep(.el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #eee;
  }

  :deep(.el-dialog__body) {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }

  :deep(.el-dialog__footer) {
    padding: 15px 20px;
    border-top: 1px solid #eee;
  }
}

.product-detail-content {
  max-height: 80vh;
  overflow-y: auto;
  padding: 0;
}

// 顶部区域样式
.product-top-section {
  background: #fff;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;

  .product-main-info {
    display: flex;
    gap: 30px;
    align-items: flex-start;

    .product-image {
      flex-shrink: 0;
      width: 300px;

      .main-image {
        width: 100%;
        height: auto;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      }
    }

    .product-info {
      flex: 1;

      .product-title {
        font-size: 28px;
        font-weight: 700;
        color: #333;
        margin-bottom: 15px;
        line-height: 1.3;
      }

      .product-badges {
        display: flex;
        gap: 8px;
        margin-bottom: 15px;

        .product-badge {
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 12px;
          background: #f0f0f0;
          color: #666;

          &.hot-badge {
            background: #ff4d4f;
            color: white;
          }
        }
      }

      .pizza-intro {
        margin-bottom: 20px;

        .product-description {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          margin-bottom: 12px;
        }

        .base-ingredients {
          font-size: 13px;
          line-height: 1.4;

          .ingredients-label {
            color: #333;
            font-weight: 600;
          }

          .ingredients-text {
            color: #666;
          }
        }
      }

      .product-price {
        margin-bottom: 25px;

        .current-price {
          font-size: 32px;
          font-weight: 700;
          color: #e60012;
        }

        .price-range {
          font-size: 16px;
          color: #999;
          margin-left: 15px;
        }
      }

      .specs-selection {
        margin-bottom: 25px;

        .spec-title {
          font-size: 14px;
          font-weight: 600;
          color: #666;
          margin-bottom: 10px;
        }

        .size-sauce-row {
          display: flex;
          gap: 15px;
          margin-bottom: 25px;

          .size-selection,
          .sauce-selection {

            margin-bottom: 0;
          }
        }
      }

      .size-options {
        display: flex;
        gap: 8px;

        .size-option {
          border: 2px solid #e0e0e0;
          border-radius: 6px;
          padding: 6px;
          cursor: pointer;
          transition: all 0.3s ease;
          text-align: center;
          min-width: 65px;
          background: #fff;


          &:hover {
            border-color: #e60012;
            box-shadow: 0 2px 8px rgba(230, 0, 18, 0.1);
          }

          &.selected {
            border-color: #e60012;
          }

          .size-image {
            position: relative;
            margin-bottom: 4px;
            width: 100%;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;

            .size-img {
              width: 100%;
              height: 100%;
              object-fit: contain;
              border-radius: 6px;
            }

            .size-label {
              position: absolute;
              bottom: -2px;
              left: 50%;
              transform: translateX(-50%);
              font-size: 10px;
              font-weight: 600;
              color: #333;
              background: rgba(255, 255, 255, 0.9);
              border-radius: 8px;
              padding: 1px 4px;
              border: 1px solid #e0e0e0;
            }
          }

          .size-info {
            .size-title {
              font-size: 13px;
              font-weight: 600;
              color: #333;
              text-align: center;
            }
          }
        }
      }

      .sauce-options {
        display: flex;
        gap: 6px;

        .sauce-option {
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          padding: 3px;
          cursor: pointer;
          transition: all 0.3s ease;
          text-align: center;
          min-width: 40px;
          background: #fff;


          &:hover {
            border-color: #e60012;
            box-shadow: 0 2px 8px rgba(230, 0, 18, 0.1);
          }

          &.selected {
            border-color: #e60012;
          }

          .sauce-icon {
            margin-bottom: 3px;
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;

            .sauce-image {
              width: 100%;
              height: 100%;
              object-fit: contain;
              border-radius: 6px;
            }
          }

          .sauce-name {
            font-size: 9px;
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
            line-height: 1.2;
          }

          .sauce-desc {
            font-size: 8px;
            color: #666;
            line-height: 1.1;
          }
        }
      }
      }

      .quantity-selection {

        .spec-title {
          font-size: 14px;
          font-weight: 600;
          color: #666;
          margin-bottom: 10px;
        }

        .quantity-input {
          width: 120px;

          :deep(.el-input-number__decrease),
          :deep(.el-input-number__increase) {
            background: #f5f5f5;
            border-color: #e0e0e0;

            &:hover {
              background: #e60012;
              border-color: #e60012;
              color: white;
            }
          }

          :deep(.el-input__inner) {
            text-align: center;
            font-weight: 600;
          }
        }
      }

      .quantity-cart-section {
        display: flex;
        align-items: flex-end;
        gap: 20px;
        margin-bottom: 25px;

        .quantity-selection {
          .spec-title {
            font-size: 14px;
            font-weight: 600;
            color: #666;
            margin-bottom: 10px;
          }

          .custom-quantity-input {
            display: flex;
            align-items: center;
            gap: 15px;

            .quantity-display {
              font-size: 18px;
              font-weight: 600;
              color: #333;
              min-width: 32px;
              text-align: center;
              background: #f8f9fa;
              border-radius: 8px;
              padding: 6px 12px;
              border: 1px solid #e9ecef;
            }
          }
        }

        .add-to-cart-section {
          .add-cart-btn {
            background: #e60012;
            border-color: #e60012;
            font-size: 16px;
            font-weight: 600;
            padding: 12px 24px;
            height: 48px;

            &:hover {
              background: #cc0010;
              border-color: #cc0010;
            }
          }
        }
      }
    }
  }


// 中间区域样式 - 扩展选择层
.product-middle-section {
  background: #fff;
  padding: 30px 20px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e60012;
  }

  .crust-section {
    margin-bottom: 40px;

    .crust-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 15px;

      .crust-item {
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        &:hover {
          border-color: #e60012;
          box-shadow: 0 4px 12px rgba(230, 0, 18, 0.1);
        }

        &.selected {
          border-color: #e60012;
        }

        .crust-image {
          width: 100%;
          height: 60px;
          object-fit: cover;
          display: block;
        }

        .crust-name {
          font-size: 11px;
          font-weight: 500;
          color: #333;
          line-height: 1.2;
          padding: 6px 8px;
          background: #fff;
        }

        .crust-selected {
          position: absolute;
          top: 10px;
          right: 10px;
          background: #e60012;
          color: white;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
        }
      }
    }
  }
  }

  .ingredients-section {
    .base-ingredients,
    .additional-ingredients {
      margin-bottom: 40px;

      .ingredients-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 10px;

        .ingredient-card {
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          transition: all 0.3s ease;
          display: flex;
          align-items: stretch;
          overflow: hidden;
          height: 60px;

          &:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
          }

          .ingredient-image {
            width: 60px;
            height: 100%;
            object-fit: cover;
            flex-shrink: 0;
          }

          .ingredient-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 6px 8px;

            .ingredient-name {
              font-size: 12px;
              font-weight: 500;
              color: #333;
              margin-bottom: 2px;
            }

            .ingredient-price {
              font-size: 11px;
              color: #e60012;
              font-weight: 600;
              margin-bottom: 4px;
            }

            .ingredient-controls {
              display: flex;
              align-items: center;
              gap: 6px;

              .qty-circle-btn {
                width: 22px;
                height: 22px;
                font-size: 12px;
                border-width: 1px;
                box-shadow: 0 1px 3px rgba(230, 0, 18, 0.1);

                &:hover {
                  transform: scale(1.1);
                  box-shadow: 0 2px 6px rgba(230, 0, 18, 0.15);
                }
              }

              .ingredient-quantity {
                font-size: 12px;
                color: #333;
                min-width: 18px;
                text-align: center;
                font-weight: 600;
                background: #f8f9fa;
                border-radius: 3px;
                padding: 1px 4px;
                border: 1px solid #e9ecef;
              }
            }

            .ingredient-status {
              font-size: 12px;
              color: #1890ff;
              margin-bottom: 10px;
            }

            .ingredient-price {
              font-size: 14px;
              color: #e60012;
              font-weight: 600;
              margin-bottom: 10px;
            }

            .ingredient-controls {
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 10px;

              .control-btn {
                width: 32px;
                height: 32px;
                padding: 0;
                border-radius: 50%;
                font-size: 16px;
                font-weight: 600;
              }

              .quantity-display {
                font-size: 16px;
                font-weight: 600;
                min-width: 30px;
                text-align: center;
                color: #333;
              }
            }
          }
        }
      }
    }
  }

// 底部区域样式
.product-bottom-section {
  background: #f8f9fa;
  padding: 30px 20px;
  border-top: 1px solid #e0e0e0;

  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 25px;
    text-align: center;
  }

  .size-guide {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-bottom: 30px;

    .size-guide-item {
      text-align: center;

      .size-circle {
        width: 120px;
        height: 120px;
        border: 3px solid #e60012;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        position: relative;

        &.size-9 {
          width: 100px;
          height: 100px;
        }

        &.size-12 {
          width: 120px;
          height: 120px;
        }

        .size-text {
          font-size: 24px;
          font-weight: 700;
          color: #e60012;
        }
      }

      .size-info {
        .size-name {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          margin-bottom: 5px;
        }

        .size-desc {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }

  .special-notes {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;

    .note-text {
      font-size: 13px;
      color: #999;
      margin-bottom: 5px;
      line-height: 1.5;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 商品信息区域
.product-info-section {
  display: flex;
  gap: 20px;

  .product-image {
    width: 200px;
    height: 200px;
    overflow: hidden;
    border-radius: 8px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }

  .product-basic-info {
    flex: 1;

    .product-name {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0 0 10px;
      color: #333;
    }

    .product-badges {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      margin-bottom: 10px;
    }

    .product-badge {
      background: #e60012;
      color: white;
      padding: 3px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 600;

      &.hot-badge {
        background: #ff6b35;
      }
    }

    .product-desc {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
      margin-bottom: 15px;
    }

    .product-price {
      font-size: 1.8rem;
      font-weight: 700;
      color: #e60012;
    }
  }
}

// 规格选择区域
.product-specs-section {
  .section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 15px;
    color: #333;
  }

  .size-selection {
    margin-bottom: 20px;

    .size-options {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
    }

    .size-option {
      border: 2px solid #eee;
      border-radius: 8px;
      padding: 10px 15px;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 100px;
      text-align: center;

      &:hover {
        border-color: #e60012;
      }

      &.selected {
        border-color: #e60012;
        background-color: rgba(230, 0, 18, 0.05);
      }

      .size-name {
        font-weight: 600;
        margin-bottom: 5px;
      }

      .size-price {
        color: #e60012;
        font-weight: 700;
      }
    }
  }

  .crust-selection {
    margin-bottom: 20px;

    .crust-options {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
    }

    .crust-option {
      border: 2px solid #eee;
      border-radius: 8px;
      padding: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
      width: 120px;
      text-align: center;

      &:hover {
        border-color: #e60012;
      }

      &.selected {
        border-color: #e60012;
        background-color: rgba(230, 0, 18, 0.05);
      }

      .crust-image {
        width: 80px;
        height: 80px;
        object-fit: contain;
        margin-bottom: 10px;
      }

      .crust-name {
        font-weight: 600;
        margin-bottom: 5px;
        font-size: 14px;
      }

      .crust-desc {
        color: #666;
        font-size: 12px;
      }
    }
  }

  .quantity-selection {
    margin-bottom: 20px;
  }
}

// 饼底展示区域
.product-variants-section,
.product-ingredients-section {
  .section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 15px;
    color: #333;
  }

  .variants-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
  }

  .ingredients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
  }

  .sauce-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 15px;
  }

  .variant-item {
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 10px;
    border: 2px solid transparent;
    position: relative;

    &:hover {
      background-color: #f5f5f5;
    }

    &.selected {
      border-color: #e60012;
      background-color: #fff5f5;
    }

    .variant-image {
      width: 100px;
      height: 100px;
      object-fit: contain;
      margin-bottom: 8px;
      border-radius: 8px;
    }

    .variant-name {
      font-size: 13px;
      color: #333;
    }

    .variant-selected {
      position: absolute;
      top: 5px;
      right: 5px;
      background: #e60012;
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
    }
  }

  .ingredient-item {
    text-align: left;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 10px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    &.base-ingredient {
      background-color: #f0f8ff;
      border-left: 4px solid #1890ff;

      .ingredient-status {
        color: #1890ff;
        font-weight: 500;
      }
    }

    &.additional-ingredient {
      background-color: #fff;
      border-left: 4px solid #52c41a;

      .ingredient-price {
        color: #e60012;
        font-weight: 600;
      }
    }

    .ingredient-image {
      width: 60px;
      height: 60px;
      object-fit: contain;
      border-radius: 6px;
      float: left;
      margin-right: 10px;
    }

    .ingredient-info {
      overflow: hidden;
    }

    .ingredient-name {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .ingredient-status {
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
    }

    .ingredient-price {
      font-size: 12px;
      color: #e60012;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .ingredient-controls {
      display: flex;
      align-items: center;
      gap: 8px;

      .quantity-btn {
        width: 24px;
        height: 24px;
        padding: 0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .quantity-display {
        font-size: 14px;
        font-weight: 500;
        min-width: 20px;
        text-align: center;
      }
    }
  }

  .sauce-item {
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 10px;
    border: 2px solid transparent;
    position: relative;

    &:hover {
      background-color: #f5f5f5;
    }

    &.selected {
      border-color: #e60012;
      background-color: #fff5f5;
    }

    .sauce-image {
      width: 80px;
      height: 80px;
      object-fit: contain;
      margin-bottom: 8px;
      border-radius: 8px;
    }

    .sauce-info {
      .sauce-name {
        font-size: 13px;
        color: #333;
        margin-bottom: 4px;
      }

      .sauce-selected {
        font-size: 12px;
        color: #e60012;
        font-weight: 500;
      }
    }
  }
}

// 底部操作区域
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 15px;

  .total-price {
    margin-right: auto;
    font-size: 16px;

    .price-value {
      font-size: 20px;
      font-weight: 700;
      color: #e60012;
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .product-info-section {
    flex-direction: column;

    .product-image {
      width: 100%;
      height: auto;
      aspect-ratio: 1/1;
    }
  }

  .product-specs-section {
    .size-option,
    .crust-option {
      width: calc(50% - 8px);
    }
  }

  .variants-grid,
  .ingredients-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)) !important;
  }
}

// 暗色主题支持
html.dark {
  .product-detail-dialog {
    :deep(.el-dialog__header),
    :deep(.el-dialog__footer) {
      border-color: var(--el-border-color);
    }
  }

  .product-info-section {
    .product-image {
      background: var(--el-bg-color);
    }

    .product-basic-info {
      .product-name {
        color: var(--el-text-color-primary);
      }

      .product-desc {
        color: var(--el-text-color-regular);
      }
    }
  }

  .product-specs-section,
  .product-variants-section,
  .product-ingredients-section {
    .section-title {
      color: var(--el-text-color-primary);
    }
  }

  .size-option,
  .crust-option {
    border-color: var(--el-border-color) !important;

    &.selected {
      background-color: rgba(230, 0, 18, 0.1) !important;
    }
  }

  .variant-name,
  .ingredient-name {
    color: var(--el-text-color-primary) !important;
  }

  .crust-desc {
    color: var(--el-text-color-secondary) !important;
  }

  // 新布局样式
  .product-header {
    .product-title {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
    }

    .product-price {
      .current-price {
        font-size: 28px;
        font-weight: 700;
        color: #e60012;
      }

      .price-range {
        font-size: 14px;
        color: #999;
        margin-left: 10px;
      }
    }
  }

  .product-specs-section,
  .crust-selection,
  .sauce-selection,
  .product-ingredients-section,
  .quantity-selection {
    margin-bottom: 25px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
    }
  }

  .size-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;

    .size-option {
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      padding: 10px 15px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #e60012;
      }

      &.selected {
        border-color: #e60012;
        background-color: #fff5f5;
      }

      .size-name {
        font-weight: 600;
        margin-bottom: 4px;
      }

      .size-price {
        font-size: 14px;
        color: #e60012;
      }
    }
  }

  .crust-options,
  .sauce-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;

    .crust-option,
    .sauce-option {
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      padding: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      min-width: 80px;

      &:hover {
        border-color: #e60012;
      }

      &.selected {
        border-color: #e60012;
        background-color: #fff5f5;
      }

      .crust-image,
      .sauce-image {
        width: 60px;
        height: 60px;
        object-fit: contain;
        margin-bottom: 8px;
        border-radius: 4px;
      }

      .crust-name,
      .sauce-name {
        font-size: 13px;
        font-weight: 500;
        color: #333;
      }
    }
  }

  .ingredients-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;

    .ingredient-item {
      display: flex;
      align-items: center;
      padding: 10px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      }

      &.base-ingredient {
        background-color: #f0f8ff;
        border-left: 4px solid #1890ff;
      }

      &.additional-ingredient {
        background-color: #fff;
        border-left: 4px solid #52c41a;
      }

      .ingredient-image {
        width: 50px;
        height: 50px;
        object-fit: contain;
        border-radius: 4px;
        margin-right: 12px;
      }

      .ingredient-info {
        flex: 1;

        .ingredient-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 4px;
        }

        .ingredient-status {
          font-size: 12px;
          color: #1890ff;
          margin-bottom: 8px;
        }

        .ingredient-price {
          font-size: 12px;
          color: #e60012;
          font-weight: 600;
          margin-bottom: 8px;
        }

        .ingredient-controls {
          display: flex;
          align-items: center;
          gap: 8px;

          .el-button {
            width: 24px;
            height: 24px;
            padding: 0;
            border-radius: 50%;
          }

          .quantity-display {
            font-size: 14px;
            font-weight: 500;
            min-width: 20px;
            text-align: center;
          }
        }
      }
    }
  }
}

// 全局圆形按钮样式
.qty-circle-btn {
  width: 28px;
  height: 28px;
  border: 1.5px solid #e60012;
  background: #fff;
  border-radius: 50%;
  font-size: 14px;
  font-weight: 600;
  color: #e60012;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(230, 0, 18, 0.1);

  &:hover {
    background: #e60012;
    color: #fff;
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(230, 0, 18, 0.2);
  }

  &:active {
    transform: scale(0.95);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    border-color: #d9d9d9;
    color: #d9d9d9;
    box-shadow: none;

    &:hover {
      background: #fff;
      color: #d9d9d9;
      transform: none;
      box-shadow: none;
    }
  }
}

// 底部详情图片样式
.product-bottom-section {
  .detail-images {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .detail-image {
      width: 100%;
      max-width: 800px;
      margin: 0 auto;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
