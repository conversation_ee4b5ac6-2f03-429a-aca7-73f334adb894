<?php

namespace app\mce\controller;

use app\common\controller\Userend;

class Dashboard extends Userend
{
    public function initialize(): void
    {
        parent::initialize();
    }

    public function index(): void
    {
        $this->success('', [
            'remark' => get_route_remark()
        ]);
    }

    /**
     * 获取商品列表
     */
    public function getProducts(): void
    {
        // 直接返回所有商品数据，不进行任何筛选
        $products = file_get_contents('../商品列表数据.txt');
        $products = json_decode($products, true);

        // 直接返回所有商品数组
        $this->success('获取商品列表成功', array_values($products));
    }



    /**
     * 获取商品详情
     */
    public function getProductDetail(): void
    {
        $productCode = $this->request->param('productCode', '');

        if (empty($productCode)) {
            $this->error('商品代码不能为空');
        }


        $productDetail = file_get_contents('../商品详情信息数据格式.txt');
        $productDetail = json_decode($productDetail,true);

        // 添加调试日志
        error_log('商品详情数据: ' . json_encode($productDetail, JSON_UNESCAPED_UNICODE));

        $this->success('获取商品详情成功', [
            'product' => $productDetail
        ]);
    }

    /**
     * 获取商品分类列表
     */
    public function getCategories(): void
    {
        try {
            $categoriesData = file_get_contents('../分类数据.txt');
            $categoriesData = json_decode($categoriesData, true);

            // 直接返回完整的分类数据
            $this->success('获取分类列表成功', $categoriesData);

        } catch (Exception $e) {
            // 如果读取失败，返回空数组
            $this->error('获取分类列表失败: ' . $e->getMessage());
        }
    }

}