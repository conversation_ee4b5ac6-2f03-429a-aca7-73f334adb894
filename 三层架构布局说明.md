# 商品详情三层架构布局说明

## 设计理念

按照用户决策流程，将商品详情页面设计为三层架构：

1. **核心商品层（顶层）** - 核心决策信息
2. **扩展选择层（中层）** - 个性化定制选项  
3. **辅助说明层（底层）** - 全局辅助信息

## 三层架构详解

### 🎯 **第一层：核心商品层（顶层）**

**功能定位**：首屏聚焦，用户核心决策信息，视觉与操作优先级最高

**包含内容**：
- **商品图片**：300px 大图展示
- **商品名称**：28px 大标题
- **商品标签**：热门、新品等
- **商品简介**：详细描述
- **价格显示**：32px 红色大字体
- **规格选择**：
  - 尺寸选择（9英寸、12英寸）
  - 饼底选择（前3个选项）
  - 淋酱选择（前3个选项）
- **加购按钮**：200px 宽度红色按钮

**设计特点**：
```vue
<div class="product-top-section">
  <div class="product-main-info">
    <div class="product-image"><!-- 商品图片 --></div>
    <div class="product-info">
      <!-- 名称、价格、简介 -->
      <div class="specs-selection">
        <!-- 尺寸、饼底、淋酱 -->
      </div>
      <!-- 加购按钮 -->
    </div>
  </div>
</div>
```

### 🔧 **第二层：扩展选择层（中层）**

**功能定位**：承接核心商品，提供丰富定制选项，满足个性化需求

**包含内容**：
- **饼底扩展选择**：所有饼底选项的完整展示
- **配料选择**：基础配料 + 添加配料

**设计特点**：
- 通过可视化图例 + 名称展示
- 网格布局，自适应排列
- 选中状态明确标识
- 是核心商品的延伸拓展

```vue
<div class="product-middle-section">
  <div class="crust-section">
    <!-- 完整饼底选择 -->
  </div>
  <div class="ingredients-section">
    <!-- 配料选择 -->
  </div>
</div>
```

### 📋 **第三层：辅助说明层（底层）**

**功能定位**：全局辅助信息，为商品展示、用户理解兜底

**包含内容**：
- **比萨尺寸说明**：圆形图示对比
- **适用人数**：每个尺寸的建议
- **特殊说明**：注意事项和规则

**设计特点**：
- 服务全品类，优先级最低
- 保障信息完整性
- 浅灰背景区分层次

## 层级关系

### 📊 **视觉层次**
```
┌─────────────────────────────────────────────────────────┐
│                  核心商品层（顶层）                        │
│  ┌─────────────┐  ┌─────────────────────────────────┐   │
│  │   商品图片   │  │ 名称+价格+简介+规格选择+加购     │   │
│  └─────────────┘  └─────────────────────────────────┘   │
├─────────────────────────────────────────────────────────┤
│                  扩展选择层（中层）                        │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              饼底完整选择                            │ │
│  └─────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              配料选择                                │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                  辅助说明层（底层）                        │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              尺寸说明图示                            │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 🎯 **交互逻辑**
1. **认知商品**：查看图片、名称、价格、简介
2. **快速选择**：选择基本规格（尺寸、饼底、淋酱）
3. **快速购买**：直接加入购物车
4. **深度定制**：扩展选择更多饼底和配料
5. **理解规则**：查看尺寸说明和注意事项

### 🎨 **设计原则**

**信息优先级**：
- 第一层：最重要，决定购买的核心信息
- 第二层：重要，满足个性化需求
- 第三层：辅助，帮助理解和决策

**视觉权重**：
- 第一层：大字体、鲜明色彩、突出按钮
- 第二层：中等字体、网格布局、清晰分组
- 第三层：小字体、浅色背景、简洁说明

**操作流程**：
- 支持快速购买（第一层完成）
- 支持深度定制（第二层扩展）
- 提供决策支持（第三层辅助）

## 用户体验

### ✨ **核心优势**
1. **决策效率**：核心信息前置，快速决策
2. **个性化**：扩展选择满足定制需求
3. **信息完整**：三层覆盖所有必要信息
4. **操作灵活**：支持快速购买和深度定制

### 🎯 **用户路径**
- **快速用户**：第一层 → 加购
- **定制用户**：第一层 → 第二层 → 加购
- **谨慎用户**：第一层 → 第二层 → 第三层 → 加购

现在的布局完全符合三层架构的设计理念，提供了清晰的信息层次和优秀的用户体验！
