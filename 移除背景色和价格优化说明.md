# 移除背景色和价格优化说明

## 优化目标

1. 移除所有选中状态的背景颜色
2. 不显示价格信息，简化界面

## 主要修改

### 1. 移除价格显示
```vue
<!-- 修改前：显示规格和价格 -->
<div class="size-info">
  <div class="size-details">
    <span class="size-name">{{ priceItem.exceptionKey }}</span>
    <span class="size-price">{{ priceItem.exceptionText }}</span>
  </div>
</div>

<!-- 修改后：只显示规格 -->
<div class="size-info">
  <div class="size-name">{{ priceItem.exceptionKey }}</div>
</div>
```

### 2. 移除选中背景色
```scss
// 修改前：有背景色
&.selected {
  border-color: #e60012;
  background-color: #fff5f5; // 浅红背景
}

// 修改后：无背景色
&.selected {
  border-color: #e60012; // 只保留红色边框
}
```

### 3. 简化样式结构
```scss
// 修改前：复杂的并排布局
.size-info {
  .size-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    // ...
  }
}

// 修改后：简单的居中布局
.size-info {
  .size-name {
    font-size: 13px;
    font-weight: 600;
    color: #333;
    text-align: center;
  }
}
```

## 视觉效果对比

### 修改前
```
┌─────────────┐  ┌─────────────┐
│ [尺寸图片]  │  │ [尺寸图片]  │
│ 9英寸 ¥39   │  │12英寸 ¥59   │ ← 显示价格
└─────────────┘  └─────────────┘
     ↑ 选中时有浅红背景色
```

### 修改后
```
┌─────────────┐  ┌─────────────┐
│ [尺寸图片]  │  │ [尺寸图片]  │
│   9英寸     │  │   12英寸    │ ← 不显示价格
└─────────────┘  └─────────────┘
     ↑ 选中时只有红色边框，无背景色
```

## 设计理念

### 🎯 **简洁性**
- **信息精简**：只显示必要的尺寸信息
- **视觉干净**：移除多余的背景色干扰
- **焦点突出**：让用户专注于尺寸选择

### 🎨 **视觉统一**
- **边框指示**：统一使用红色边框表示选中
- **背景一致**：所有状态保持白色背景
- **层次清晰**：图片 → 文字的简单层次

### 📱 **用户体验**
- **选择明确**：红色边框清晰指示选中状态
- **信息聚焦**：用户专注于尺寸而非价格
- **视觉舒适**：减少颜色变化的视觉干扰

## 应用范围

### 尺寸选择
- ✅ 移除选中背景色：`background-color: #fff5f5`
- ✅ 保留选中边框：`border-color: #e60012`
- ✅ 移除价格显示：不显示 `priceItem.exceptionText`

### 淋酱选择
- ✅ 移除选中背景色：`background-color: #fff5f5`
- ✅ 保留选中边框：`border-color: #e60012`
- ✅ 保留其他信息：酱料名称和描述

## 交互状态

### 默认状态
```scss
.size-option, .sauce-option {
  border: 2px solid #e0e0e0;
  background: #fff;
}
```

### 悬停状态
```scss
&:hover {
  border-color: #e60012;
  box-shadow: 0 2px 8px rgba(230, 0, 18, 0.1);
  // 保持白色背景
}
```

### 选中状态
```scss
&.selected {
  border-color: #e60012;
  // 移除背景色，保持白色
}
```

## 用户体验优势

### ✨ **视觉清爽**
- **减少颜色干扰**：统一的白色背景
- **焦点明确**：红色边框清晰指示选择
- **信息精简**：只显示核心的尺寸信息

### 🎯 **选择体验**
- **状态明确**：边框颜色变化清晰可见
- **操作简单**：点击选择，视觉反馈直接
- **认知负担低**：减少不必要的信息干扰

### 📐 **设计一致性**
- **统一风格**：所有选择项保持一致的视觉风格
- **品牌色彩**：只在必要时使用品牌红色
- **简洁美观**：符合现代简约设计趋势

## 技术细节

### CSS 优化
- **移除背景色属性**：减少CSS代码量
- **简化布局结构**：从flex布局回到简单布局
- **统一选中状态**：所有组件使用相同的选中样式

### 性能考虑
- **减少重绘**：背景色变化会触发重绘
- **简化DOM**：移除不必要的嵌套结构
- **统一样式**：减少CSS规则复杂度

## 最终效果

现在的选择组件：
- ✅ **视觉简洁**：无背景色干扰，界面更清爽
- ✅ **信息精准**：只显示核心的尺寸信息
- ✅ **选择明确**：红色边框清晰指示选中状态
- ✅ **体验统一**：所有选择项保持一致的交互体验
- ✅ **设计现代**：符合简约设计趋势

完全符合您的要求，提供了更简洁清爽的选择体验！
