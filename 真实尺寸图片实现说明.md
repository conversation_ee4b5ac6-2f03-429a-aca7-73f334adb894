# 真实尺寸图片实现说明

## 修改内容

将尺寸选择中的SVG饼图替换为商品详情中真实的尺寸图片，使用 `sizeList` 数组中的图片数据。

## 数据来源

### sizeList 数组结构
```json
"sizeList": [
  {
    "code": "S",
    "name": "9\"",
    "nameEN": "9\"",
    "imageURL1": "https://pic.dominos.com.cn:8443/ApiPicture/20190703/5a20784031bf4b43889b1a938a5d7d29.jpg",
    "imageURL2": "https://pic.dominos.com.cn:8443/ApiPicture/20190703/5a20784031bf4b43889b1a938a5d7d29.jpg",
    "sort": 5
  },
  {
    "code": "L",
    "name": "12\"",
    "nameEN": "12\"",
    "imageURL1": "https://pic.dominos.com.cn:8443/ApiPicture/20190703/1c7c68293d494dfc900747bd7c653bb5.jpg",
    "imageURL2": "https://pic.dominos.com.cn:8443/ApiPicture/20190703/1c7c68293d494dfc900747bd7c653bb5.jpg",
    "sort": 10
  }
]
```

## 技术实现

### 1. 模板修改
```vue
<!-- 修改前：SVG饼图 -->
<div class="pizza-chart">
  <svg width="30" height="30" viewBox="0 0 30 30" class="chart-svg">
    <circle cx="15" cy="15" r="12" fill="#4A90E2" stroke="#fff" stroke-width="1"/>
    <path d="M 15 15 L 15 3 A 12 12 0 0 1 24 9 Z" fill="#E74C3C"/>
    <path d="M 15 15 L 24 9 A 12 12 0 0 1 27 15 Z" fill="#F39C12"/>
    <path d="M 15 15 L 27 15 A 12 12 0 0 1 21 24 Z" fill="#27AE60"/>
  </svg>
  <div class="size-label">{{ priceItem.exceptionKey.replace('英寸', '"') }}</div>
</div>

<!-- 修改后：真实图片 -->
<div class="size-image">
  <img 
    :src="getSizeImage(priceItem.exceptionKey)" 
    :alt="priceItem.exceptionKey" 
    class="size-img"
    @error="handleImageError"
  />
  <div class="size-label">{{ priceItem.exceptionKey.replace('英寸', '"') }}</div>
</div>
```

### 2. 图片获取方法
```javascript
// 获取尺寸图片
const getSizeImage = (sizeName: string) => {
  if (!productResource.value?.sizeList) return '/placeholder.svg'
  
  // 根据尺寸名称找到对应的尺寸信息
  const sizeInfo = productResource.value.sizeList.find((size: any) => {
    return size.name === sizeName || size.name.includes(sizeName.replace('英寸', ''))
  })
  
  return sizeInfo?.imageURL1 || sizeInfo?.imageURL2 || '/placeholder.svg'
}
```

### 3. CSS样式调整
```scss
.size-image {
  position: relative;
  margin-bottom: 6px;
  display: flex;
  justify-content: center;

  .size-img {
    width: 30px;
    height: 30px;
    object-fit: contain; // 保持图片比例
    border-radius: 4px;
  }

  .size-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 10px;
    font-weight: 600;
    color: #333;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
```

## 匹配逻辑

### 尺寸名称映射
- **9英寸** → 匹配 `sizeList` 中 `name: "9\""`
- **12英寸** → 匹配 `sizeList` 中 `name: "12\""`

### 查找策略
1. **精确匹配**：`size.name === sizeName`
2. **包含匹配**：`size.name.includes(sizeName.replace('英寸', ''))`
3. **降级处理**：如果找不到，使用占位图片

### 图片优先级
1. **首选**：`imageURL1`
2. **备选**：`imageURL2`
3. **降级**：`/placeholder.svg`

## 优势对比

### 修改前（SVG饼图）
- ❌ **虚拟图标**：不是真实的比萨尺寸图片
- ❌ **固定样式**：所有尺寸显示相同的饼图
- ❌ **缺乏真实感**：用户无法直观感受尺寸差异

### 修改后（真实图片）
- ✅ **真实展示**：使用官方提供的尺寸对比图片
- ✅ **直观对比**：用户可以直观看到9英寸和12英寸的差异
- ✅ **品牌一致性**：与官方图片保持一致
- ✅ **用户体验**：更真实的购买决策参考

## 错误处理

### 图片加载失败
- **@error 事件**：绑定 `handleImageError` 方法
- **降级显示**：自动切换到占位图片
- **用户体验**：确保界面不会出现破损图片

### 数据缺失处理
- **sizeList 为空**：返回占位图片
- **匹配失败**：返回占位图片
- **网络问题**：通过 error 事件处理

## 性能考虑

### 图片优化
- **尺寸适配**：30x30px 小尺寸显示
- **object-fit: contain**：保持图片比例
- **懒加载**：可考虑添加懒加载优化

### 缓存策略
- **CDN 缓存**：图片来自官方CDN
- **浏览器缓存**：自动缓存已加载图片
- **重复使用**：同一图片在多处使用时共享缓存

## 用户体验提升

### 🎯 **直观性**
- 真实的比萨尺寸对比图片
- 用户可以直观感受大小差异
- 提供更准确的购买决策参考

### 🎨 **视觉效果**
- 与官方品牌图片保持一致
- 更专业的视觉呈现
- 增强用户信任度

### 📱 **响应式设计**
- 30x30px 适合紧凑布局
- 保持图片清晰度
- 适配不同屏幕尺寸

现在尺寸选择使用真实的官方图片，为用户提供更直观准确的尺寸对比体验！
