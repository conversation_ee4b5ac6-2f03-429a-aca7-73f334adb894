# 顶部区域优化说明

## 修改内容

按照要求对顶部核心商品层进行了优化：

1. **移除饼底选择**：从顶部规格选择中移除饼底选项
2. **添加数量选择**：在加入购物车按钮前添加数量选择

## 修改前后对比

### 修改前
```
顶部核心商品层：
- 商品图片 + 商品信息
- 规格选择：
  - 尺寸选择
  - 饼底选择 ❌
  - 淋酱选择
- 加入购物车按钮 ❌ (缺少数量选择)
```

### 修改后
```
顶部核心商品层：
- 商品图片 + 商品信息
- 规格选择：
  - 尺寸选择
  - 淋酱选择
- 数量选择 ✅
- 加入购物车按钮 ✅
```

## 设计理念

### 🎯 **核心商品层简化**
- **聚焦核心决策**：只保留最基础的选择项
- **减少认知负担**：避免过多选项干扰决策
- **快速购买路径**：尺寸 + 淋酱 + 数量 → 购买

### 🔧 **饼底选择下沉**
- **移至扩展层**：饼底选择移到中间的扩展选择层
- **深度定制**：需要特殊饼底的用户可在中间层选择
- **层次清晰**：基础选择 vs 扩展选择分层明确

### 📊 **数量选择前置**
- **购买必需**：数量是购买的必要信息
- **操作连贯**：选择数量 → 加入购物车的自然流程
- **视觉统一**：与其他规格选择保持一致的样式

## 技术实现

### HTML 结构
```vue
<div class="specs-selection">
  <!-- 尺寸选择 -->
  <div class="size-selection">...</div>
  
  <!-- 淋酱选择 -->
  <div class="sauce-selection">...</div>
</div>

<!-- 数量选择 -->
<div class="quantity-selection">
  <h4 class="spec-title">数量</h4>
  <el-input-number v-model="quantity" :min="1" :max="99" size="large" />
</div>

<!-- 加入购物车按钮 -->
<div class="add-to-cart-section">
  <el-button type="primary" size="large" @click="addToCart">
    加入购物车
  </el-button>
</div>
```

### CSS 样式
```scss
.quantity-selection {
  margin-bottom: 25px;

  .spec-title {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    margin-bottom: 10px;
  }

  .quantity-input {
    width: 120px;
    
    // 自定义按钮样式
    :deep(.el-input-number__decrease),
    :deep(.el-input-number__increase) {
      &:hover {
        background: #e60012;
        border-color: #e60012;
        color: white;
      }
    }
  }
}
```

## 用户体验优化

### ✨ **简化决策流程**
1. **查看商品**：图片、名称、价格、简介
2. **基础选择**：尺寸 + 淋酱
3. **设置数量**：选择购买数量
4. **快速购买**：直接加入购物车
5. **可选定制**：如需特殊饼底，可在中间层选择

### 🎯 **层次更清晰**
- **顶层**：核心决策信息（尺寸、淋酱、数量）
- **中层**：扩展定制选项（饼底、配料）
- **底层**：辅助说明信息（尺寸说明）

### 🚀 **操作更流畅**
- **减少选择**：顶层只保留必要选择
- **逻辑清晰**：从基础到扩展的自然流程
- **购买便捷**：数量选择紧邻购买按钮

## 设计亮点

### 📱 **移动端友好**
- 数量选择器使用 Element Plus 组件
- 响应式设计，适配不同屏幕
- 触摸友好的按钮尺寸

### 🎨 **视觉一致性**
- 数量选择与其他规格选择样式统一
- 标题字体、间距保持一致
- 悬停效果使用品牌红色

### ⚡ **性能优化**
- 减少顶层渲染元素
- 简化交互逻辑
- 提升首屏加载体验

现在顶部核心商品层更加简洁明确，为用户提供了更好的决策体验！
