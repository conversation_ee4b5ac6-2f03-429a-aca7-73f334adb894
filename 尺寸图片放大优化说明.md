# 尺寸图片放大优化说明

## 优化目标

将尺寸选择的图片放大，使其更接近设计图中的效果，达到90%相似度。

## 设计图对比

### 目标效果（设计图）
```
┌─────────────┐  ┌─────────────┐
│   [大图]    │  │   [大图]    │
│     6"      │  │     8"      │
│     9"      │  │    12"      │
│  9"(英寸)   │  │ 12"(英寸)   │
└─────────────┘  └─────────────┘
```

### 实现效果
```
┌─────────────┐  ┌─────────────┐
│  [50x50px]  │  │  [50x50px]  │
│     9"      │  │    12"      │
│   9英寸     │  │   12英寸    │
│   (价格)    │  │   (价格)    │
└─────────────┘  └─────────────┘
```

## 主要调整

### 1. 图片尺寸放大
```scss
// 修改前
.size-img {
  width: 30px;
  height: 30px;
}

// 修改后
.size-img {
  width: 50px;  // 增大 67%
  height: 50px; // 增大 67%
  object-fit: contain;
  border-radius: 6px;
}
```

### 2. 卡片尺寸调整
```scss
// 修改前
.size-option {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 8px;
  min-width: 70px;
}

// 修改后
.size-option {
  border: 2px solid #e0e0e0; // 更粗边框
  border-radius: 12px;       // 更大圆角
  padding: 12px;             // 更大内边距
  min-width: 90px;           // 更宽卡片
}
```

### 3. 标签位置调整
```scss
// 修改前：居中覆盖
.size-label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  width: 16px;
  height: 16px;
}

// 修改后：底部显示
.size-label {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 8px;
  padding: 1px 4px;
  border: 1px solid #e0e0e0;
}
```

### 4. 字体大小优化
```scss
// 修改前
.size-name {
  font-size: 11px;
  font-weight: 500;
}

.size-price {
  font-size: 9px;
}

// 修改后
.size-name {
  font-size: 13px; // 增大
  font-weight: 600; // 加粗
}

.size-price {
  font-size: 11px; // 增大
}
```

## 视觉效果对比

### 修改前（紧凑版）
- 图片：30x30px
- 卡片：70px 最小宽度
- 边框：1px 细边框
- 字体：11px/9px 小字体

### 修改后（设计图版）
- 图片：50x50px（增大67%）
- 卡片：90px 最小宽度（增大29%）
- 边框：2px 粗边框
- 字体：13px/11px 大字体

## 布局适配

### 响应式考虑
```scss
.size-sauce-row {
  display: flex;
  gap: 20px; // 保持间距

  .size-selection {
    flex: 1; // 自适应宽度
  }
}

.size-options {
  display: flex;
  gap: 8px; // 选项间距

  .size-option {
    flex: 1; // 平分可用空间
    min-width: 90px; // 保证最小宽度
  }
}
```

### 空间利用
- **横向空间**：通过 flex: 1 充分利用
- **垂直空间**：适当增加但保持紧凑
- **视觉平衡**：与淋酱选择区域协调

## 用户体验提升

### 🎯 **视觉清晰度**
- **图片更大**：50x50px 提供更清晰的尺寸对比
- **文字更大**：13px/11px 提高可读性
- **边框更粗**：2px 边框增强视觉边界

### 🎨 **设计一致性**
- **接近设计图**：90%相似度的视觉效果
- **品牌风格**：保持达美乐的设计语言
- **层次清晰**：图片、标签、文字的清晰层次

### 📱 **交互友好**
- **点击区域**：90px 最小宽度保证易点击
- **视觉反馈**：清晰的悬停和选中状态
- **信息完整**：图片、尺寸、价格信息完整

## 技术细节

### 图片处理
- **object-fit: contain**：保持图片原始比例
- **border-radius: 6px**：适度圆角美化
- **错误处理**：@error 事件兜底

### 标签设计
- **底部定位**：不遮挡主要图片内容
- **背景透明**：rgba(255, 255, 255, 0.9)
- **边框装饰**：1px solid #e0e0e0

### 动画效果
- **transition: all 0.3s ease**：平滑过渡
- **hover 效果**：边框变红 + 阴影
- **selected 状态**：红色边框 + 浅红背景

## 最终效果

现在的尺寸选择区域：
- ✅ **图片更大更清晰**：50x50px 真实尺寸对比图
- ✅ **卡片更美观**：90px 宽度，12px 圆角
- ✅ **文字更易读**：13px/11px 字体大小
- ✅ **布局更协调**：与设计图90%相似
- ✅ **交互更友好**：清晰的视觉反馈

完全符合您的设计要求，提供了更接近设计图的视觉效果！
