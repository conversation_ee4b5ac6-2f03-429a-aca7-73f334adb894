# 尺寸和淋酱并排布局说明

## 修改内容

将顶部核心商品层中的尺寸选择和淋酱选择改为并排显示，优化空间利用和视觉效果。

## 布局变化

### 修改前
```
规格选择区域：
┌─────────────────────────────────────┐
│ 尺寸                                │
│ [9英寸] [12英寸]                    │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│ 淋酱                                │
│ [番茄酱] [白酱] [BBQ酱]             │
└─────────────────────────────────────┘
```

### 修改后
```
规格选择区域：
┌─────────────────┬───────────────────┐
│ 尺寸            │ 淋酱              │
│ [9英寸][12英寸] │ [番茄酱][白酱]... │
└─────────────────┴───────────────────┘
```

## 技术实现

### HTML 结构
```vue
<div class="specs-selection">
  <!-- 尺寸和淋酱并排选择 -->
  <div class="size-sauce-row">
    <!-- 尺寸选择 -->
    <div class="size-selection">
      <h4 class="spec-title">尺寸</h4>
      <div class="size-options">
        <!-- 尺寸选项 -->
      </div>
    </div>

    <!-- 酱料选择 -->
    <div class="sauce-selection">
      <h4 class="spec-title">淋酱</h4>
      <div class="sauce-options">
        <!-- 酱料选项 -->
      </div>
    </div>
  </div>
</div>
```

### CSS 样式
```scss
.specs-selection {
  .size-sauce-row {
    display: flex;
    gap: 30px; // 左右间距

    .size-selection,
    .sauce-selection {
      flex: 1; // 各占一半宽度
      margin-bottom: 0;
    }
  }

  .size-options {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .size-option {
      flex: 1; // 自适应宽度
      min-width: 70px;
      padding: 8px 12px;
      // ... 其他样式
    }
  }

  .sauce-options {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .sauce-option {
      flex: 1; // 自适应宽度
      min-width: 50px;
      padding: 6px;
      // ... 其他样式
    }
  }
}
```

## 设计优化

### 🎨 **视觉效果**
- **空间利用**：横向空间得到更好利用
- **视觉平衡**：左右对称的布局更美观
- **紧凑布局**：减少垂直空间占用

### 📱 **响应式设计**
- **弹性布局**：使用 `flex: 1` 自适应宽度
- **换行支持**：`flex-wrap: wrap` 支持小屏幕换行
- **最小宽度**：保证选项的最小可点击区域

### 🎯 **用户体验**
- **逻辑分组**：尺寸和淋酱作为基础选择并排显示
- **操作便捷**：相关选择在同一视线范围内
- **视觉层次**：清晰的标题和选项分组

## 样式细节

### 尺寸选择
- **最小宽度**：70px
- **内边距**：8px 12px
- **字体大小**：标题13px，价格11px
- **自适应**：flex: 1 平分可用空间

### 淋酱选择
- **最小宽度**：50px
- **内边距**：6px
- **图片尺寸**：32x32px
- **字体大小**：10px
- **自适应**：flex: 1 平分可用空间

### 交互效果
- **悬停状态**：边框变红色
- **选中状态**：红色边框 + 浅红背景
- **过渡动画**：0.3s 平滑过渡

## 用户体验优势

### ✨ **空间效率**
- 减少垂直空间占用
- 提高信息密度
- 更好的屏幕利用率

### 🎯 **操作便捷**
- 基础选择项在同一行
- 减少视线移动距离
- 提高选择效率

### 📐 **视觉和谐**
- 左右对称的平衡感
- 统一的选项样式
- 清晰的信息层次

现在尺寸选择和淋酱选择已经成功并排显示，提供了更紧凑和高效的布局！
