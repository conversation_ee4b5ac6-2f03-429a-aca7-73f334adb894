# 底酱+淋酱紧挨尺寸布局说明

## 布局调整

将"底酱+淋酱"选择区域与尺寸选择区域紧挨着显示，减少间距，形成更紧凑的布局。

## 当前布局结构

### HTML 结构
```vue
<div class="specs-selection">
  <!-- 尺寸和淋酱并排选择 -->
  <div class="size-sauce-row">
    <!-- 尺寸选择 -->
    <div class="size-selection">
      <h4 class="spec-title">尺寸</h4>
      <div class="size-options">
        <!-- 尺寸选项 -->
      </div>
    </div>

    <!-- 底酱+淋酱选择 -->
    <div class="sauce-selection">
      <h4 class="spec-title">底酱+淋酱</h4>
      <div class="sauce-options">
        <!-- 酱料选项 -->
      </div>
    </div>
  </div>
</div>
```

### CSS 布局
```scss
.size-sauce-row {
  display: flex;
  gap: 15px;        // 减少间距 20px → 15px
  margin-bottom: 25px;

  .size-selection,
  .sauce-selection {
    flex: 1;        // 各占一半宽度
    margin-bottom: 0;
  }
}
```

## 间距优化

### 修改前
```scss
.size-sauce-row {
  gap: 20px;  // 较大间距
}
```

### 修改后
```scss
.size-sauce-row {
  gap: 15px;  // 更紧凑的间距
}
```

## 视觉效果

### 布局示意图
```
┌─────────────────────────────────────────────────────────┐
│                    规格选择区域                          │
│  ┌─────────────────┬─────────────────────────────────┐   │
│  │ 尺寸            │ 底酱+淋酱                       │   │
│  │ ┌─────┐┌─────┐  │ ┌─────┐┌─────┐┌─────┐         │   │
│  │ │ 9"  ││ 12" │  │ │酱料1││酱料2││酱料3│         │   │
│  │ └─────┘└─────┘  │ └─────┘└─────┘└─────┘         │   │
│  └─────────────────┴─────────────────────────────────┘   │
│                      ↑ 15px 间距                        │
└─────────────────────────────────────────────────────────┘
```

## 设计特点

### 🎯 **紧凑布局**
- **减少间距**：15px 间距让两个区域更紧挨
- **视觉连贯**：相关选择项在同一视线范围
- **空间高效**：充分利用横向空间

### 🎨 **视觉平衡**
- **左右对称**：尺寸和酱料各占一半
- **高度一致**：两个区域高度基本一致
- **间距适中**：既紧凑又不拥挤

### 📱 **用户体验**
- **选择便捷**：相关选择在同一行
- **逻辑清晰**：尺寸 → 酱料的自然流程
- **操作高效**：减少视线移动距离

## 响应式考虑

### 桌面端
- **并排显示**：充分利用宽屏空间
- **间距适中**：15px 提供清晰分隔
- **比例协调**：flex: 1 平分宽度

### 移动端
- **自适应**：flex 布局自动适应
- **触摸友好**：保持足够的点击区域
- **间距合理**：15px 在小屏幕上也合适

## 与其他元素的关系

### 上方元素
- **商品信息**：名称、价格、简介
- **间距分隔**：与规格选择有清晰分隔

### 下方元素
- **数量选择**：紧接在规格选择下方
- **加购按钮**：最终的操作按钮

### 整体流程
```
商品图片 + 商品信息
        ↓
   规格选择区域
   ┌─────────┬─────────┐
   │  尺寸   │ 底酱+淋酱│ ← 紧挨着显示
   └─────────┴─────────┘
        ↓
     数量选择
        ↓
    加入购物车
```

## 标题更新

### 酱料区域标题
```vue
<h4 class="spec-title">底酱+淋酱</h4>
```

- **明确说明**：底酱和淋酱的组合选择
- **用户理解**：清楚表达选择的是什么
- **术语准确**：符合比萨制作术语

## 技术实现

### Flexbox 布局
```scss
display: flex;        // 弹性布局
gap: 15px;           // 子元素间距
margin-bottom: 25px; // 与下方元素间距

.size-selection,
.sauce-selection {
  flex: 1;           // 平分可用空间
  margin-bottom: 0;  // 移除底部间距
}
```

### 间距控制
- **水平间距**：15px gap 控制左右间距
- **垂直间距**：25px margin-bottom 与下方分隔
- **内部间距**：各选项内部保持原有间距

## 最终效果

现在的布局：
- ✅ **紧挨显示**：尺寸和底酱+淋酱紧挨着
- ✅ **间距适中**：15px 间距既紧凑又清晰
- ✅ **视觉连贯**：相关选择在同一视线范围
- ✅ **操作便捷**：用户可以快速完成选择
- ✅ **布局协调**：与整体设计风格一致

完全符合您的要求，底酱+淋酱选择区域现在紧挨着尺寸选择区域！
