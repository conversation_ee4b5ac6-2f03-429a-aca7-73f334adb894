# 商品详情垂直分层布局重构说明

## 布局设计

按照要求，已将 ProductDetailDialog.vue 重构为垂直分层布局：

### 🎨 **整体布局**

```
┌─────────────────────────────────────────────────────────────┐
│                    商品详情对话框                              │
├─────────────────────────────────────────────────────────────┤
│                      顶部区域                                │
│  ┌─────────────┐  ┌─────────────────────────────────────┐   │
│  │             │  │  托斯卡纳风情芝浓三文鱼比萨           │   │
│  │   商品图片   │  │  ┌─────────────────────────────────┐ │   │
│  │             │  │  │ 热门 新品                        │ │   │
│  │             │  │  └─────────────────────────────────┘ │   │
│  │             │  │  商品简介描述...                     │   │
│  │             │  │  ￥68  原价￥78                      │   │
│  │             │  │  ┌─────────────────────────────────┐ │   │
│  │             │  │  │ 9英寸 ￥68  12英寸 ￥88         │ │   │
│  │             │  │  └─────────────────────────────────┘ │   │
│  │             │  │  当前饼底：经典手拍                   │   │
│  │             │  │  当前酱料：番茄酱                     │   │
│  │             │  │  ┌─────────────────────────────────┐ │   │
│  │             │  │  │      加入购物车                  │ │   │
│  └─────────────┘  │  └─────────────────────────────────┘ │   │
│                   └─────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                      中间区域                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   饼底选择                               │ │
│  │  [可可熔岩] [火山饼底] [经典手拍] [芝士流心] ...         │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   酱料选择                               │ │
│  │  [番茄酱] [白酱] [BBQ酱] [蒜蓉酱] ...                   │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   基础配料                               │ │
│  │  [烟熏三文鱼片 -1+] [芝士 -1+] [洋葱 -1+] ...           │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   添加配料                               │ │
│  │  [雪球芝士 +￥8 -0+] [培根 +￥12 -0+] ...               │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      底部区域                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 比萨尺寸说明                             │ │
│  │    ○ 9"        ○ 12"                                   │ │
│  │   9英寸       12英寸                                     │ │
│  │  1-2人享用    2-3人享用                                 │ │
│  │                                                         │ │
│  │  *特殊饼底可能影响比萨厚度                               │ │
│  │  *配料数量可根据个人喜好调整                             │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 顶部区域 (product-top-section)

### 1. 左侧商品图片
- **尺寸**：300px 固定宽度
- **圆角阴影**：现代化视觉效果
- **高质量显示**：清晰的商品展示

#### 商品标题和标签
- **大标题**：28px 粗体显示商品名称
- **标签系统**：热门、新品等标签展示
- **商品简介**：详细的商品描述

#### 价格显示
- **当前价格**：32px 大字体红色显示
- **价格范围**：显示不同规格的价格区间

#### 尺寸选择
- **横向布局**：9英寸、12英寸等选项
- **选中状态**：红色边框高亮
- **价格显示**：每个尺寸对应的价格

#### 饼底+酱料说明
- **当前选择**：显示已选择的饼底和酱料
- **实时更新**：根据用户选择动态更新

#### 加入购物车按钮
- **大按钮**：200px 宽度，50px 高度
- **红色主题**：达美乐品牌色
- **悬停效果**：颜色加深反馈

## 中间区域 (product-middle-section)

### 1. 饼底选择区
- **网格布局**：自适应网格排列
- **图片展示**：80x80px 饼底图片
- **选中标记**：右上角勾选图标
- **悬停效果**：边框颜色变化和阴影

### 2. 酱料选择区
- **紧凑布局**：小图标 + 名称
- **多选支持**：根据商品配置
- **选中反馈**：视觉状态变化

### 3. 配料选择区

#### 基础配料
- **蓝色渐变**：浅蓝色渐变背景
- **蓝色左边框**：4px 蓝色强调边框
- **数量控制**：圆形 +/- 按钮
- **默认1份**：超出部分收费

#### 添加配料
- **绿色渐变**：浅绿色渐变背景
- **绿色左边框**：4px 绿色强调边框
- **价格显示**：显示单价
- **数量控制**：从0开始计费

## 底部区域 (product-bottom-section)

### 1. 比萨尺寸说明
- **圆形图示**：不同大小的圆形表示尺寸
- **尺寸对比**：9英寸 vs 12英寸
- **适用人数**：每个尺寸的建议人数

### 2. 特殊说明
- **注意事项**：特殊饼底说明
- **配料说明**：数量调整和收费说明

## 样式特点

### 🎨 **视觉设计**
- **垂直分层**：清晰的三层结构
- **圆角设计**：12px 统一圆角
- **渐变背景**：配料卡片使用渐变背景
- **阴影效果**：悬停时的阴影反馈
- **颜色体系**：
  - 主色：#e60012 (达美乐红)
  - 蓝色：#1890ff (基础配料)
  - 绿色：#52c41a (添加配料)
  - 背景：#f8f9fa (底部区域)

### 📱 **响应式设计**
- **网格布局**：自适应网格系统
- **弹性布局**：内容自适应
- **滚动支持**：整体区域支持滚动
- **最小宽度**：保证内容完整显示

### 🔧 **交互体验**
- **悬停效果**：所有可点击元素
- **选中状态**：明确的视觉反馈
- **实时更新**：价格和状态即时变化
- **按钮状态**：禁用状态处理
- **选中标记**：右上角勾选图标

## 技术实现

### 布局结构
```vue
<div class="product-detail-content">
  <div class="product-top-section">
    <!-- 商品基本信息 -->
  </div>
  <div class="product-middle-section">
    <!-- 饼底、酱料、配料选择 -->
  </div>
  <div class="product-bottom-section">
    <!-- 尺寸说明 -->
  </div>
</div>
```

### CSS 关键样式
```scss
.product-top-section {
  .product-main-info {
    display: flex;
    gap: 30px;
    align-items: flex-start;
  }
}

.product-middle-section {
  .crust-grid,
  .sauce-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 20px;
  }

  .ingredients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }
}

.product-bottom-section {
  .size-guide {
    display: flex;
    justify-content: center;
    gap: 60px;
  }
}
```

## 用户体验

### 📖 **信息层次**
1. **顶部**：商品基本信息、价格、尺寸选择、快速购买
2. **中间**：详细的定制选择（饼底、酱料、配料）
3. **底部**：尺寸说明和特殊注意事项

### 🎯 **操作流程**
1. **查看商品**：图片、名称、简介、价格
2. **选择尺寸**：9英寸或12英寸
3. **快速购买**：直接加入购物车
4. **详细定制**：
   - 选择饼底样式
   - 选择酱料类型
   - 调整基础配料数量
   - 添加额外配料
5. **查看说明**：了解尺寸对比和注意事项

### ✨ **视觉反馈**
- **选中状态**：红色边框 + 背景色变化
- **悬停效果**：阴影和颜色变化
- **实时价格**：配置变化时即时更新
- **选中标记**：右上角勾选图标
- **渐变背景**：配料卡片的美观展示
- **圆形图示**：直观的尺寸对比

### 🎨 **设计亮点**
- **分层清晰**：顶部信息 → 中间选择 → 底部说明
- **视觉层次**：重要信息突出显示
- **品牌一致**：达美乐红色主题贯穿
- **用户友好**：直观的操作界面
- **信息完整**：从基本信息到详细定制

现在 ProductDetailDialog.vue 已经完全按照垂直分层的布局要求进行了重构，提供了清晰的信息层次和优秀的用户体验！
