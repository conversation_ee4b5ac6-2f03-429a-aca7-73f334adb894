# 商品详情交互功能实现

## 功能概述

已将 ProductDetailDialog.vue 从纯展示组件升级为完整的交互式商品定制组件，支持：

### 🎯 **核心功能**

1. **饼底选择**
   - 可点击选择不同饼底
   - 视觉反馈（选中状态高亮）
   - 选中标记显示

2. **配料分类管理**
   - **基础配料**：可调整数量，默认1份，超出部分收费
   - **添加配料**：可选择数量，显示价格，从0开始计费

3. **酱料选择**
   - 单选或多选模式
   - 根据商品配置限制选择数量
   - 选中状态视觉反馈

4. **实时价格计算**
   - 基础价格 + 配料价格
   - 数量变化时自动更新
   - 调试信息显示计算过程

## 界面布局

### 1. 饼底选择区域
```vue
<div class="product-variants-section">
  <h3>选择饼底</h3>
  <div class="variants-grid">
    <!-- 可点击的饼底选项 -->
  </div>
</div>
```

### 2. 酱料选择区域
```vue
<div class="product-sauce-section">
  <h3>选择酱料</h3>
  <div class="sauce-grid">
    <!-- 可点击的酱料选项 -->
  </div>
</div>
```

### 3. 基础配料选择
```vue
<div class="product-ingredients-section">
  <h3>基础配料</h3>
  <!-- 数量选择器：- [数量] +，默认1份 -->
  <!-- 超出1份的部分按价格收费 -->
</div>
```

### 4. 添加配料选择
```vue
<div class="product-ingredients-section">
  <h3>添加配料</h3>
  <!-- 数量选择器：- [数量] + -->
</div>
```

## 交互功能

### 1. 饼底选择
```javascript
const selectPastry = (pastryInfo) => {
  selectPastryInfo.value = pastryInfo
  calculatePrice()
}
```

### 2. 基础配料数量控制
```javascript
const increaseBaseIngredient = (ingredient) => {
  ingredient.buyNum++
  ingredient.isDefault = ingredient.buyNum > 0 ? 1 : 0
  calculatePrice()
}

const decreaseBaseIngredient = (ingredient) => {
  if (ingredient.buyNum > 0) {
    ingredient.buyNum--
    ingredient.isDefault = ingredient.buyNum > 0 ? 1 : 0
    calculatePrice()
  }
}
```

### 3. 附加配料数量控制
```javascript
const increaseIngredient = (ingredient) => {
  ingredient.buyNum++
  ingredient.isDefault = ingredient.buyNum > 0 ? 1 : 0
  calculatePrice()
}

const decreaseIngredient = (ingredient) => {
  if (ingredient.buyNum > 0) {
    ingredient.buyNum--
    ingredient.isDefault = ingredient.buyNum > 0 ? 1 : 0
    calculatePrice()
  }
}
```

### 3. 酱料选择
```javascript
const changeSauce = (sauceInfo) => {
  const { sauceSelectNumber, sauceList: sauceListData } = productInfo.value
  
  if (sauceSelectNumber === 1) {
    // 单选模式：取消其他选择
    sauceListData.forEach((info) => {
      info.isDefault = info.code === sauceInfo.code ? 1 : 0
    })
  } else {
    // 多选模式：检查数量限制
    // ...
  }
}
```

### 5. 价格计算（更新版）
```javascript
const calculatePrice = () => {
  let basePrice = 0 // 基础价格
  let ingredientPrice = 0 // 配料价格

  // 计算基础价格（根据尺寸）

  // 计算基础配料价格变化（超过1份的部分收费）
  baseIngredientsList.value.forEach(ingredient => {
    if (ingredient.buyNum > 1 && ingredient.productPrice) {
      const extraQuantity = ingredient.buyNum - 1
      ingredientPrice += parseFloat(ingredient.productPrice) * extraQuantity
    }
  })

  // 计算附加配料价格（全部数量收费）
  additionalIngredientsList.value.forEach(ingredient => {
    if (ingredient.buyNum > 0 && ingredient.productPrice) {
      ingredientPrice += parseFloat(ingredient.productPrice) * ingredient.buyNum
    }
  })

  pizzaPrice.value = (basePrice + ingredientPrice) * productNum.value
}
```

## 样式设计

### 1. 选择状态样式
```scss
.variant-item, .sauce-item {
  &.selected {
    border-color: #e60012;
    background-color: #fff5f5;
  }
}
```

### 2. 配料卡片样式（更新版）
```scss
.ingredient-item {
  &.base-ingredient {
    background-color: #f0f8ff; // 基础配料背景（浅蓝色）
    border-left: 4px solid #1890ff; // 蓝色左边框

    .ingredient-status {
      color: #1890ff;
      font-weight: 500;
    }
  }

  &.additional-ingredient {
    background-color: #fff; // 添加配料背景（白色）
    border-left: 4px solid #52c41a; // 绿色左边框

    .ingredient-price {
      color: #e60012;
      font-weight: 600;
    }
  }
}
```

### 3. 数量控制器样式
```scss
.ingredient-controls {
  .quantity-btn {
    width: 24px;
    height: 24px;
    border-radius: 50%;
  }
  
  .quantity-display {
    min-width: 20px;
    text-align: center;
  }
}
```

## 数据流程

```
用户操作 → 更新数据状态 → 重新计算价格 → 更新界面显示
```

### 关键数据：
- `selectPastryInfo`: 选中的饼底
- `selectSauceInfo`: 选中的酱料
- `baseIngredientsList`: 基础配料列表
- `additionalIngredientsList`: 添加配料列表
- `pizzaPrice`: 计算后的总价格

## 调试功能

添加了详细的调试信息显示：
- 各类数据的数量统计
- 当前选中的饼底和酱料
- 实时价格显示
- 数据加载状态

## 用户体验

### 视觉反馈：
- ✅ 选中状态高亮显示
- ✅ 悬停效果
- ✅ 选中标记图标
- ✅ 价格实时更新

### 交互体验：
- ✅ 点击选择饼底
- ✅ 点击选择酱料
- ✅ 按钮控制配料数量
- ✅ 禁用状态处理（数量为0时）

### 信息展示：
- ✅ 基础配料标记为"基础配料"，可调整数量
- ✅ 添加配料显示价格，可选择数量
- ✅ 选中酱料显示"已选择"
- ✅ 实时总价显示（包含配料变化）

### 价格逻辑：
- **基础配料**：默认1份包含在基础价格中，超出部分按单价收费
- **添加配料**：所有数量都按单价收费
- **实时计算**：任何数量变化都会立即更新总价

### 视觉区分：
- **基础配料**：浅蓝色背景 + 蓝色左边框
- **添加配料**：白色背景 + 绿色左边框
- **数量控制**：统一的圆形按钮设计

现在 ProductDetailDialog.vue 已经是一个功能完整的商品定制组件，支持用户进行个性化的披萨定制，包括基础配料和添加配料的完全自定义！
