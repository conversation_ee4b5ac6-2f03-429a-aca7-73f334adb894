# 尺寸 Code 判断显示说明

## 功能需求

根据尺寸的 code 值来显示对应的标准化标题，而不是直接显示原始的尺寸名称。

## 实现方案

### 1. 模板判断逻辑
```vue
<div class="size-info">
  <div class="size-title" v-if="getSizeCode(priceItem.exceptionKey) == '7'">7"(英寸)</div>
  <div class="size-title" v-if="getSizeCode(priceItem.exceptionKey) == 'S'">9"(英寸)</div>
  <div class="size-title" v-if="getSizeCode(priceItem.exceptionKey) == 'L'">12"(英寸)</div>
</div>
```

### 2. 获取 Code 方法
```javascript
// 获取尺寸对应的 code
const getSizeCode = (sizeName: string) => {
  if (!productResource.value?.sizeList) return ''
  
  // 根据尺寸名称找到对应的尺寸信息
  const sizeInfo = productResource.value.sizeList.find((size: any) => {
    return size.name === sizeName || size.name.includes(sizeName.replace('英寸', ''))
  })
  
  return sizeInfo?.code || ''
}
```

## 数据映射关系

### sizeList 数据结构
```json
"sizeList": [
  {
    "code": "7",     // 7英寸
    "name": "7\"",
    "imageURL1": "...",
    "sort": 1
  },
  {
    "code": "S",     // 9英寸 (小号)
    "name": "9\"", 
    "imageURL1": "...",
    "sort": 5
  },
  {
    "code": "L",     // 12英寸 (大号)
    "name": "12\"",
    "imageURL1": "...",
    "sort": 10
  }
]
```

### Code 到标题的映射
| Code | 显示标题 | 说明 |
|------|----------|------|
| `'7'` | `7"(英寸)` | 7英寸比萨 |
| `'S'` | `9"(英寸)` | 9英寸比萨 (小号) |
| `'L'` | `12"(英寸)` | 12英寸比萨 (大号) |

## 技术实现

### 查找逻辑
```javascript
// 1. 精确匹配尺寸名称
size.name === sizeName

// 2. 包含匹配（去掉"英寸"后缀）
size.name.includes(sizeName.replace('英寸', ''))
```

### 匹配示例
- **输入**: `"9英寸"` → **匹配**: `name: "9\""` → **返回**: `code: "S"`
- **输入**: `"12英寸"` → **匹配**: `name: "12\""` → **返回**: `code: "L"`
- **输入**: `"7英寸"` → **匹配**: `name: "7\""` → **返回**: `code: "7"`

## 显示效果

### 修改前（直接显示原始名称）
```
┌─────────────┐  ┌─────────────┐
│ [尺寸图片]  │  │ [尺寸图片]  │
│   9英寸     │  │   12英寸    │
└─────────────┘  └─────────────┘
```

### 修改后（根据 code 显示标准标题）
```
┌─────────────┐  ┌─────────────┐
│ [尺寸图片]  │  │ [尺寸图片]  │
│  9"(英寸)   │  │ 12"(英寸)   │
└─────────────┘  └─────────────┘
```

## 优势分析

### 🎯 **标准化显示**
- **统一格式**：所有尺寸都使用 `X"(英寸)` 格式
- **简洁明了**：使用英寸符号 `"` 更简洁
- **国际化友好**：英寸符号是国际通用标准

### 🎨 **视觉一致性**
- **格式统一**：避免不同数据源的格式差异
- **长度一致**：`7"(英寸)`、`9"(英寸)`、`12"(英寸)` 长度相近
- **美观整齐**：整体视觉效果更整齐

### 🔧 **灵活性**
- **易于扩展**：新增尺寸只需添加对应的判断条件
- **数据独立**：不依赖后端数据格式
- **可控性强**：前端完全控制显示内容

## 扩展性考虑

### 新增尺寸支持
```vue
<!-- 如果有新的尺寸，只需添加对应判断 -->
<div class="size-title" v-if="getSizeCode(priceItem.exceptionKey) == 'M'">10"(英寸)</div>
<div class="size-title" v-if="getSizeCode(priceItem.exceptionKey) == 'XL'">14"(英寸)</div>
```

### 配置化优化
```javascript
// 可以进一步优化为配置化
const sizeCodeMap = {
  '7': '7"(英寸)',
  'S': '9"(英寸)', 
  'L': '12"(英寸)',
  'M': '10"(英寸)',
  'XL': '14"(英寸)'
}

const getSizeTitle = (sizeName: string) => {
  const code = getSizeCode(sizeName)
  return sizeCodeMap[code] || sizeName
}
```

## 错误处理

### 数据缺失处理
- **sizeList 为空**：返回空字符串，不显示标题
- **找不到匹配**：返回空字符串，不显示标题
- **code 不匹配**：不显示任何标题（v-if 条件不满足）

### 降级显示
```vue
<!-- 可以添加降级显示 -->
<div class="size-title" v-if="getSizeCode(priceItem.exceptionKey) == '7'">7"(英寸)</div>
<div class="size-title" v-if="getSizeCode(priceItem.exceptionKey) == 'S'">9"(英寸)</div>
<div class="size-title" v-if="getSizeCode(priceItem.exceptionKey) == 'L'">12"(英寸)</div>
<!-- 降级显示原始名称 -->
<div class="size-title" v-if="!getSizeCode(priceItem.exceptionKey)">{{ priceItem.exceptionKey }}</div>
```

## CSS 样式

### 样式类名更新
```scss
// 修改前
.size-name {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  text-align: center;
}

// 修改后
.size-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  text-align: center;
}
```

## 最终效果

现在的尺寸显示：
- ✅ **标准化格式**：统一使用 `X"(英寸)` 格式
- ✅ **Code 驱动**：根据 sizeList 中的 code 值判断
- ✅ **视觉一致**：所有尺寸显示格式统一
- ✅ **易于维护**：清晰的判断逻辑，易于扩展
- ✅ **错误处理**：数据缺失时的兜底机制

完全符合您的需求，提供了基于 code 判断的标准化尺寸显示！
